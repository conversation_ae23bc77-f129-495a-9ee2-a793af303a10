export default {
  'marketing.agent.tab.whatsapp': 'WhatsApp',
  'marketing.agent.search.btn': 'Search',
  'marketing.agent.activity.name': 'Activity Name:',
  'marketing.agent.add.title': 'Add WhatsApp Marketing Agent',
  'marketing.agent.select.activity': 'Select Marketing Activity',
  'marketing.agent.name': 'Marketing Agent Name',
  'marketing.agent.select.channel': 'Select Marketing Channel',
  'marketing.agent.select.whatsapp.channel': 'Select WhatsApp Channel',
  'marketing.agent.interaction.type': 'Interaction Type',
  'marketing.agent.interaction.type.one.time': 'One-time Marketing',
  'marketing.agent.cancel': 'Cancel',
  'marketing.agent.confirm': 'Confirm',
  'marketing.agent.activity.select.name': 'Please select activity name',

  // Testing related
  'marketing.test.modal.title': 'WhatsApp Marketing Test',
  'marketing.test.phone.label': 'Please enter test phone numbers (up to 5)',
  'marketing.test.phone.placeholder': 'Please enter phone number',
  'marketing.test.phone.required': 'Please enter at least one phone number',
  'marketing.test.phone.invalid': 'Please enter a valid phone number format',
  'marketing.test.add.phone': 'Add Phone Number',
  'marketing.test.cancel': 'Cancel',
  'marketing.test.send': 'Send',
  'marketing.test.send.success': 'Test message sent successfully',
  'marketing.test.send.error': 'Failed to send, please try again',

  'marketing.agent.create.step.basic.info': 'Marketing Basic Information',
  'marketing.agent.create.title': 'Add WhatsApp Marketing Agent',
  'marketing.agent.create.step.target.customer':
    'Target Customer & Marketing Time',
  'marketing.agent.create.cancel': 'Cancel',
  'marketing.agent.create.next': 'Next',
  'marketing.agent.create.finish': 'Save',

  // Edit related
  'marketing.agent.edit.title': 'Edit WhatsApp Marketing Agent',
  'marketing.agent.edit.finish': 'Save Changes',

  'marketing.agent.status.start': 'Start Now',
  'marketing.agent.status.pause': 'Pause Now',
  'marketing.agent.status.end': 'Activity Ended',

  'marketing.agent.activity.name': 'Activity Name:',
  'marketing.agent.channel.name': 'Channel Name:',
};
