import TelephoneExpenses from '../../pages/meteringBilling/telephoneExpenses';

export default {
  //日志
  'workTable.setting.loggor.title': '日志',
  'workTable.setting.loggor.button': '下载日志',
  // 计量计费菜单
  'metering.billing': '账单',
  'metering.billing.1': '概览',
  'metering.billing.2': '外部智能体AIGC套餐包',
  'metering.billing.3': '座席辅助AIGC套餐包',
  'metering.billing.4': '电话机器人套餐包',
  'metering.billing.5': '话务费',
  //座席辅助AIGC套餐包
  'agent.AIGC.package.title': '座席辅助AIGC套餐包',
  'agent.AIGC.package.echart.title': '座席辅助AIGC计费次数趋势图',
  'agent.AIGC.package.table.type.1': '系统赠送',
  'agent.AIGC.package.table.type.2': '企业购买',

  //电话机器人套餐包
  'phone.robot.package.title': '电话机器人套餐包',
  'phone.robot.package.echart.left.title': '电话机器人使用趋势图',
  'phone.robot.package.echart.right.title': '电话机器人各号码使用分钟数占比',
  'phone.robot.package.table.1': '电话号码',
  'phone.robot.package.table.2': '使用分钟数',
  'phone.robot.package.table.3': '分钟数',
  'phone.robot.package.table.3.tips': '剩余可调用分钟数',

  //告警规则
  'alarm.rule.title': '告警规则',
  'alarm.rule.title.step.1': '告警规则',
  'alarm.rule.title.step.2': '告警通知方式',
  'alarm.rule.title.step.1.1': '话务费告警',
  'alarm.rule.title.step.1.1.content':
    '当话务费剩余金额小于等于{number}元，进行{alarm}',
  'alarm.rule.title.step.alarm.yellow': '橙色告警',
  'alarm.rule.title.step.alarm.red': '红色告警',
  'alarm.rule.title.step.1.2': '外部智能体AIGC调用次数告警',
  'alarm.rule.title.step.1.2.content':
    '当外部智能体AIGC剩余可调用次数小于等于{number}次，进行{alarm}',
  'alarm.rule.title.step.1.3': '座席辅助AIGC调用次数告警',
  'alarm.rule.title.step.1.3.content':
    '当座席辅助AIGC剩余可调用次数小于等于{number}次，进行{alarm}',
  'alarm.rule.title.step.1.4': '电话机器人剩余分钟数告警',
  'alarm.rule.title.step.1.4.content':
    '当电话机器人剩余分钟数小于等于{number}分钟，进行{alarm}',
  'alarm.rule.title.step.2.checkbox': '开启邮件通知',
  'alarm.rule.title.step.2.checkbox.tips':
    '当出现告警，会给以下邮箱发送邮件通知',
  'alarm.rule.title.step.2.select': '通知用户',
  'alarm.rule.title.step.2.select.p': '选择用户',

  // 计量计费概览页面
  'over.view.title': '概览',
  'over.view.version.1': '基础版',
  'over.view.version.2': '高级版',
  'over.view.version.3': '专业版',
  'over.view.version.4': '企业版',
  'over.view.user.num': '用户数量：',
  'over.view.user.num.unit': '个',
  'over.view.user.management': '用户管理',
  'over.view.ai.agent.num': '智能体数量：',
  'over.view.ai.agent.management': '智能体管理',
  'over.view.overview.telephone.charges': '话务费概览',
  'over.view.current.balance': '当前余额',
  'over.view.go.recharge': '去充值',
  'over.view.user.num.unit.yuan': '元',
  'over.view.user.num.unit.zi': '字',
  'over.view.consumer.trends.june': '最近6月消费趋势',
  'over.view.knowledge.base.space': '知识库空间',
  'over.view.knowledge.base.num': '知识库数量：',
  'over.view.external.intelligent.agent.usage': '外部智能体AIGC用量',
  'over.view.external.intelligent.agent.usage.1': '座席辅助AIGC用量',
  'over.view.external.intelligent.agent.usage.2': '电话机器人用量',

  // 外部智能体AIGC套餐包
  'external.intelligent.agent.AIGC.package.title': '外部智能体AIGC套餐包',
  'external.intelligent.agent.AIGC.package': 'AIGC套餐包',
  'external.intelligent.agent.AIGC.package.data.analysis': '数据分析',
  'external.intelligent.agent.AIGC.package.select.time': '选择时间',
  'external.intelligent.agent.AIGC.package.select.time.placeholder':
    '请选择时间',
  'external.intelligent.agent.AIGC.package.select.time.one.month': '近一个月',
  'external.intelligent.agent.AIGC.package.select.time.six.month': '近六个月',
  'external.intelligent.agent.AIGC.package.select.time.one.year': '近一年',
  'external.intelligent.agent.AIGC.package.select.time.customize': '自定义',
  'external.intelligent.agent.AIGC.package.time.frame': '时间范围',
  'external.intelligent.agent.AIGC.package.call.frequency.trend.chart':
    '外部智能体AIGC计费次数趋势图',
  'external.intelligent.agent.AIGC.package.proportion.channel.call.frequency':
    '外部智能体AIGC各渠道计费次数占比',
  'external.intelligent.agent.AIGC.package.call.frequency.details':
    '使用次数明细',
  'external.intelligent.agent.AIGC.package.call.frequency.details.1':
    '使用明细',
  'external.intelligent.agent.AIGC.package.table.date': '日期',
  'external.intelligent.agent.AIGC.package.table.channel': '渠道',
  'external.intelligent.agent.AIGC.package.table.number.calls': '调用次数',
  'external.intelligent.agent.AIGC.package.table.billing.frequency': '计费次数',
  'external.intelligent.agent.AIGC.package.remaining.callable.times':
    '剩余可调用次数',
  'external.intelligent.agent.AIGC.package.go.buy': '去购买',
  'external.intelligent.agent.AIGC.package.unit.times': '次',
  'external.intelligent.agent.AIGC.package.select.buy.time': '购买时间',
  'external.intelligent.agent.AIGC.package.select.status': '状态：',
  'external.intelligent.agent.AIGC.package.select.status.in.effect': '生效中',
  'external.intelligent.agent.AIGC.package.select.status.expired': '已过期',
  'external.intelligent.agent.AIGC.package.select.status.exhausted': '已耗尽',
  'external.intelligent.agent.AIGC.package.table.type': '类型',
  'external.intelligent.agent.AIGC.package.table.expiration.date': '到期时间',
  'external.intelligent.agent.AIGC.package.table.status': '状态',
  'over.view.external.intelligent.agent.usage.title':
    '外部智能体AIGC可调用次数',
  'over.view.external.intelligent.agent.usage.1.title':
    '座席辅助AIGC可调用次数',
  'ver.view.external.intelligent.agent.usage.2.title': '电话机器人可使用分钟数',

  // 话务费
  'telephone.expenses.tab.1': '充值记录',
  'telephone.expenses.trend.telephone.charges': '话务费趋势',
  'telephone.expenses.proportion.telephone.call.usage': '话务费使用占比',
  'telephone.expenses.cost.details.title': '费用明细',
  'telephone.expenses.table.fee.type': '收费类型',
  'telephone.expenses.table.fee.type.placeholder': '请选择收费类型',
  'customerInformation.table.nation.placeholder': '请选择国家',
  'telephone.expenses.table.usage.unit.price': '用量单价',
  'telephone.expenses.table.subtotal.expenses': '费用小计',
  'telephone.expenses.table.money.unit': '美元',
  'phone.robot.package.table.1.placeholder': '请输入电话号码回车查询~',

  'recharge.record.table.recharge.time': '充值时间',
  'recharge.record.table.recharge.amount': '充值金额',
  'recharge.record.table.remaining.amount': '剩余金额',
  'phone.robot.package.table.1.system': '系统电话',
  'phone.robot.package.table.1.system.placeholder': '请选择系统电话',
  'over.view.overview.telephone.telephone': '话务费概览',
  'over.view.overview.telephone.telephone.balance': '当前余额',
};
