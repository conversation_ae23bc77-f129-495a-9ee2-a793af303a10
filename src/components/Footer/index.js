import { useState, useEffect } from 'react';
import { FormattedMessage, useHistory, Link } from 'umi';
import styles from './index.less';
import weixin from '@/assets/Footer/weixin.png';
import weibo from '@/assets/Footer/weibo.png';
import zhihu from '@/assets/Footer/zhihu.png';
import toutiao from '@/assets/Footer/toutiao.png';
import Facebook from '@/assets/Footer/Facebook.png';
import lingying from '@/assets/Footer/lingying.png';
import phone from '@/assets/Footer/phone.png';
import logo from '@/assets/logo.png';
import WhatsAppQrCode from '@/assets/whatsapp-qr-code.jpg';
import { Modal, Popover } from 'antd';
const Footer = () => {
  const history = useHistory();
  useEffect(() => {
    if (window.location.href.includes('connectnow.cn')) {
      setShowFooterRecord(true);
    } else {
      setShowFooterRecord(false);
    }
  }, []);
  const [showFooterRecord, setShowFooterRecord] = useState(true);
  const product = [
    {
      title: '产品',
      id: 'footer.product',
      list: [
        {
          name: '全渠道接入',
          link: '/productChannel',
          id: 'footer.product.1',
        },
        {
          name: 'AI 呼叫中心',
          link: '/productAiCallCenter',
          id: 'footer.product.2',
        },
        {
          name: 'AIGC 智能客服',
          link: '/productAIGCCustomerService',
          id: 'footer.product.3',
        },
        {
          name: 'AIGC 座席辅助',
          link: '/productAIGCAssistant',
          id: 'footer.product.4',
        },
        {
          name: '智能工单',
          link: '/productSmartWorkOrder',
          id: 'footer.product.5',
        },
        {
          name: 'AI 电话机器人',
          link: '/productAiVoiceRobot',
          id: 'footer.product.6',
        },
        {
          name: '视频客服',
          link: '/productVideoCustomerService',
          id: 'footer.product.7',
        },
        {
          name: '数据报表',
          link: '/productDataReport',
          id: 'footer.product.8',
        },
        {
          name: 'AIGC Marketing',
          link: '/productAIGCMarketing',
          id: 'footer.product.9',
        },
      ],
    },
    {
      title: '解决方案',
      id: 'footer.solution',
      list: [
        {
          name: '金融行业',
          link: '/solutionFinance',
          id: 'footer.solution.1',
        },
        {
          name: '零售行业',
          link: '/solutionRetail',
          id: 'footer.solution.2',
        },
        {
          name: '制造行业',
          link: '/solutionManufacturing',
          id: 'footer.solution.3',
        },
        {
          name: '消费电子',
          link: '/solutionConsumerElectronics',
          id: 'footer.solution.4',
        },
        {
          name: '新能源行业',
          link: '/solutionNewEnergy',
          id: 'footer.solution.5',
        },
      ],
    },
    {
      title: '资源',
      id: 'footer.resource',
      list: [
        // {
        //   name: '帮助中心',
        //   link: '/resourcesHelpCenter',
        //   id: 'footer.resource.1',
        // },
        {
          name: '博客',
          link: '/resourcesBlog',
          id: 'footer.resource.2',
        },
        {
          name: '公司新闻',
          link: '/companyNews',
          id: 'header.company.news',
        },
      ],
    },
    {
      title: '公司',
      id: 'footer.company',
      list: [
        {
          name: '关于我们',
          link: '',
          id: 'footer.company.1',
        },
        {
          name: '用户协议',
          link: '/userTerms',
          id: 'footer.company.2',
        },
        {
          name: '隐私政策',
          link: '/privacyPolicy',
          id: 'footer.company.3',
        },
        {
          name: 'Cookie政策',
          link: '/cookiePolicy',
          id: 'footer.company.4',
        },
        {
          name: '产品合规使用指南',
          link: '/complianceGuide',
          id: 'footer.company.5',
        },
        {
          name: '欧盟AI ACT遵从性说明',
          link: '/euAiActCompliance',
          id: 'footer.company.6',
        },
        {
          name: 'GDPR遵从性说明',
          link: '/gdprCompliance',
          id: 'footer.company.7',
        },
      ],
    },
  ];
  const [isNoBg, setIsNoBg] = useState(false);
  useEffect(() => {
    // 判断是否在无背景的页面
    const pathname = history.location.pathname;
    const noBgUrl = [
      '/home',
      '/partner',
      '/resourcesHelpCenter',
      '/resourcesBlog',
      '/joinUs',
    ];
    if (noBgUrl.includes(pathname)) {
      setIsNoBg(true);
    }
  }, []);
  const [weixinModalOpen, setWeixinModalOpen] = useState(false);
  const WeiXinModal = () => {
    return (
      <Modal
        open={weixinModalOpen}
        footer={null}
        width={400}
        centered
        onCancel={() => setWeixinModalOpen(false)}
      >
        <img
          style={{ width: '100%' }}
          src={require('@/assets/homePageNew/wxQrcode.png')}
        />
      </Modal>
    );
  };

  const content = (
    <div style={{ width: '160px' }}>
      <img style={{ width: '160px' }} src={WhatsAppQrCode} />
    </div>
  );

  return (
    <div className={styles.footerContainer}>
      <div
        className={styles.footerContent}
        style={{ backgroundColor: isNoBg ? 'transparent' : '#ecf1ff' }}
      >
        <div className={styles.center}>
          <div className={styles.content}>
            {product.map((item, index) => {
              return (
                <div className={styles.item} key={index}>
                  <div className={styles.title}>
                    <FormattedMessage
                      id={item.id}
                      defaultMessage={item.title}
                    />
                  </div>
                  {item.list.map((subItem, subIndex) => {
                    return subItem.link ? (
                      <Link to={subItem.link} key={subIndex}>
                        <FormattedMessage
                          id={subItem.id}
                          defaultMessage={subItem.name}
                        />
                        <div className={styles.arrowWrapper}>
                          <div className={styles.arrow}></div>
                        </div>
                      </Link>
                    ) : (
                      <Link key={subIndex}>
                        <FormattedMessage
                          id={subItem.id}
                          defaultMessage={subItem.name}
                        />
                        <div className={styles.arrowWrapper}>
                          <div className={styles.arrow}></div>
                        </div>
                      </Link>
                    );
                  })}
                </div>
              );
            })}
          </div>
          <div className={styles.externalLink}>
            <div className={styles.logo}>
              <Link to="/home">
                <img src={logo} alt="logo" />
              </Link>
            </div>
            {/* 分开显示社交媒体图标 */}
            <div className={styles.iconList}>
              {showFooterRecord && (
                <>
                  <img
                    src={weixin}
                    alt="weixin"
                    onClick={() => setWeixinModalOpen(true)}
                  />
                  <a href="https://weibo.com/u/7928574282" target="_blank">
                    <img src={weibo} alt="weibo" />
                  </a>
                  <img src={zhihu} alt="zhihu" />
                  <a
                    href="https://www.toutiao.com/c/user/token/MS4wLjABAAAAeBkHgDK6aMBKeU2sq1FLm3son6DnrBeU0rT1vA17qGS2UXULNrPcg7Jx8F6Ot5Xq/?source=list&log_from=642718b7ea40a_1742441948171"
                    target="_blank"
                  >
                    <img src={toutiao} alt="toutiao" />
                  </a>
                </>
              )}
              {!showFooterRecord && (
                <>
                  <a
                    href="https://www.facebook.com/profile.php?id=61566031101589"
                    target="_blank"
                  >
                    <img src={Facebook} alt="Facebook" />
                  </a>
                  <a
                    href="https://www.linkedin.com/company/connectnow-ai-technology/?viewAsMember=true"
                    target="_blank"
                  >
                    <img src={lingying} alt="lingying" />
                  </a>
                  <Popover content={content} title={null}>
                    <img src={phone} alt="phone" />
                  </Popover>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      {showFooterRecord && (
        <div className={styles.footerRecord}>
          <div className={styles.company}>
            <FormattedMessage
              id="footer.record.company"
              defaultMessage="深圳星途启航科技有限公司"
            />
          </div>
          <div className={styles.icp}>
            <span>
              <FormattedMessage
                id="footer.icp"
                defaultMessage="京ICP备16004950号-1"
              />
            </span>
            <span>
              <FormattedMessage
                id="footer.icpRecord"
                defaultMessage="京公网安备 11010502042997"
              />
            </span>
          </div>
        </div>
      )}
      {!showFooterRecord && (
        <div className={styles.footerRecord}>
          <div className={styles.company}>
            © {new Date().getFullYear()} ConnectNow All Rights Reserved
          </div>
        </div>
      )}
      <WeiXinModal />
    </div>
  );
};

export default Footer;
