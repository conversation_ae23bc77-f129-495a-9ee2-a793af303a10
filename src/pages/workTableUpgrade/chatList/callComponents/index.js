import React, {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from 'react';
import { notifyMe } from '@/utils/utils';
import styles from './index.less';
import moment from 'moment';
import {
  Input,
  Button,
  Tooltip,
  notification,
  Select,
  Spin,
  Space,
  Drawer,
  Alert,
  Modal,
} from 'antd';
import * as Flags from 'react-flags-select';
import 'amazon-connect-streams';
import 'amazon-connect-chatjs';
import {
  CallIcon,
  CallTimeIcon,
  CallTimeSmallIcon,
  CallTimeSmallBaiIcon,
  CallEndTimeIcon,
  CallCloseIcon,
  CallConnectFastIcon,
  CallSliIcon,
  CallSlinoIcon,
  CallKeyBoardIcon,
  CallKeepIcon,
  CallPlayIcon,
  DrawerCloseIcon,
  InputCloseIcon,
  QuickConnectBtn,
  MultipleEndBtn,
} from '../icon.js';
import agent from '@/assets/agent.svg';
import ccpErrorImg from '@/assets/ccpError.png';
import { CloseSquareFilled, ExclamationCircleFilled } from '@ant-design/icons';
import {
  parsePhoneNumber,
  isValidNumber,
  parsePhoneNumberFromString,
} from 'libphonenumber-js';
import {
  useDispatch,
  useSelector,
  getIntl,
  history,
  FormattedMessage,
} from 'umi';
const { confirm } = Modal;
const googleLanguage = JSON.parse(localStorage.getItem('languageLocal'));
const CallComponents = forwardRef(({ getPhoneCurrentInfo }, ref) => {
  const dispatch = useDispatch();
  const {
    user,
    connectListOld,
    selectedConnect,
    websocketStatus,
    agentStatus,
  } = useSelector(({ layouts }) => ({
    user: layouts.user,
    connectListOld: layouts.connectList,
    selectedConnect: layouts.selectedConnect,
    websocketStatus: layouts.websocketStatus,
    agentStatus: layouts.agentStatus,
  }));
  /**
   * 通话列表
   * connectId  实例Id 唯一
   * ticketId  工单id 唯一
   * keyNumberArea 区号
   * keyNumber  电话号码
   * timeKeeping 通话时间
   * role  身份
   * callingStatus 通话中的状态
   * connectionId 每建立一个联系人，产生的对应id
   *每次连接，不管是多人还是单人，connectId，ticketId，agent都是唯一的
   connectionId不是唯一的，每一个connectionId代表两个端点间的对话状态（保持联系，静音之类的）
   */
  let [callList, setCallList] = useState({
    connectId: '',
    ticketId: '',
    contactId: '',
    userList: [
      {
        connectionId: '',
        keyNumberArea: '',
        keyNumber: '',
        timeKeeping: 0,
        role: '',
        callingStatus: [], //结合CallingStatus状态
        status: '', //结合CallingShow状态
        name: '',
      },
    ],
  });
  let [contactLanguageLabel, setContactLanguageLabel] = useState('');
  let [keyboardValue, setKeyboardValue] = useState('');
  let [keyNumberArea, setKeyNumberArea] = useState(''); //区号
  let [keyNumber, setKeyNumber] = useState(''); //电话号码
  //通话状态下是否保持中或者静音
  //keep 保持
  //sil 静音
  //connect 快速连接
  let [callingStatus, setCallingStatus] = useState([]);
  let [timeKeeping, setTimeKeeping] = useState(0); ///通话时间
  //多种状态控制界面各种展示及逻辑
  // initial  初始状态
  //callACW   挂断状态
  //callOut   座席呼出状态
  //callIn   客户呼入状态
  //calling   通话中
  //multiple 多方通话
  //videoCallin 视频来电
  //videoCalling 视频通话中
  //videoACW   video挂断状态
  let [callingShow, setCallingShow] = useState('');
  let [drawerOpen, setDrawerOpen] = useState(false);
  let [connectList, setConnectList] = useState([]); //实例下拉
  let [loginUrl, setLoginUrl] = useState(''); //sso登录url
  let [ccpLoading, setCcpLoading] = useState(false);
  let [quickConnectLoading, setQuickConnectLoading] = useState(false);
  let [connectUserList, setConnectUserList] = useState([]); //快速连接用户列表
  let [filteredData, setFilteredData] = useState([]); //模糊搜索后的快速连接列表的用户
  let [regionTimezones, setRegionTimezones] = useState([]); //国家前缀
  let [videoRTCCapabilities, setVideoRTCCapabilities] = useState(false); //是否web聊天语音
  let [videoAttributes, setVideoAttributes] = useState(null); //web聊天语音暴露的对象
  let [ccpError, setCcpError] = useState(false); //电话ccp是否产生错误
  let [currentQCUser, setCurrentQCUser] = useState(null); //快速连接列表中选中的用户
  let [chatPhone, setChatPhone] = useState(''); //从聊天渠道转过来的电话号码
  let [chatPhoneType, setChatPhoneType] = useState(false); //从聊天渠道转过来的电话号码
  let [alertShow, setAlertShow] = useState(false); //报错公告
  let [alertText, setAlertText] = useState(''); //报错信息
  let [ticketInfo, setTicketInfo] = useState(null); //工单信息 传递父组件
  let [allowReload, setAllowReload] = useState(false); //工单信息 传递父组件
  let [callLogsExist, setCallLogsExist] = useState(false); //刷新前是否在通话中
  let [queues, setQueues] = useState(null); //坐席队列信息
  let [keepInterval, setKeepInterval] = useState(0); //全局的计数器，用于addPhoneContactDetail计算保持通话时间，单位秒
  const downKeyAudio = new Audio(
    `https://${process.env.DOMAIN_NAME_OVER}/static-icon/prompt_sound/ui-sound-1.wav`,
  );
  /**
   * 处理实例id的value是JSON字符串问题
   */
  useEffect(() => {
    let connectListTemp =
      connectListOld?.length > 0
        ? connectListOld
        : JSON.parse(localStorage.getItem('connectList'));
    let connectListNew = connectListTemp?.map(item => {
      return {
        label: item.label,
        value: JSON.parse(item.value).connectId,
        option: JSON.parse(item.value),
      };
    });

    setConnectList(connectListNew);
  }, [connectListOld]);

  /**
   * 监听通话过程中按下键盘事件
   */
  useEffect(() => {
    return () => {
      // 清理事件监听器
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);
  /**
   * SSO相关逻辑，初始话ccp
   */
  useEffect(() => {
    if (
      selectedConnect &&
      selectedConnect.identityManagementType !== 'SAML' &&
      selectedConnect.connectUrl
    ) {
      setLoginUrl('');
      try {
        init(selectedConnect);
      } catch {
        console.log('setCcpError1');
        setCcpError(true);
      }
    } else {
      getLoginUrl({
        connectId: selectedConnect.connectId,
      });
    }
    //绑定到connectId
    handleCallList('connectId', selectedConnect.connectId);
    //获取国旗列表
    systemRegionTimezones();
  }, [selectedConnect]);
  useEffect(() => {
    if (loginUrl) {
      try {
        init(selectedConnect, loginUrl);
      } catch {
        console.log('setCcpError2');
        setCcpError(true);
      }
    }
  }, [loginUrl]);
  /**
   * 监听layout上线、下线动作，电话tab同步
   */
  useEffect(() => {
    console.log(agentStatus, '自动websocketStatus,agentStatus');
    //确保初始化完成
    try {
      if (!ccpLoading) {
        connect.agent(agent => {
          if (agent) {
            var state = agent.getNextState();
            console.log(agentStatus, state, '同步自动上下线1');
            //上线
            if (agentStatus?.split('_')?.[0] === '1') {
              //自动上线
              let state = agent.getAgentStates().filter(function(state) {
                return state.type === connect.AgentStateType.ROUTABLE;
              })[0];
              agent.setState(
                state,
                {
                  success: function() {
                    console.log('自动上线成功！！！！');
                  },
                  failure: function(err) {
                    console.log('自动上线失败！！！！', err);
                  },
                },
                { enqueueNextState: true },
              );
            } else {
              //自动下线
              let state = agent.getAgentStates().filter(function(state) {
                return state.type === connect.AgentStateType.OFFLINE;
              })[0];
              agent.setState(
                state,
                {
                  success: function() {
                    console.log('CCP自动下线成功！！！！');
                  },
                  failure: function(err) {
                    console.log('CCP自动下线失败！！！！', err);
                  },
                },
                { enqueueNextState: true },
              );
            }
          }
        });
      }
    } catch {
      console.log('自动setCcpError4');
      // setCcpError(true);
    }
  }, [agentStatus, ccpLoading, loginUrl, selectedConnect]);

  /**
   * 注销ccp
   */
  useEffect(() => {
    if (!isValidJson(localStorage.getItem('callLog'))) {
      setCallingShow('initial');
      setCallingStatus([]);
    } else {
      setCallLogsExist(true);
    }
    return () => {
      if (connect.core) {
        let containerDiv = document.getElementById('container-div');
        connect.core.terminate();
        var iframe = containerDiv?.firstElementChild; // assumes there's nothing else in the container
        if (iframe) {
          containerDiv?.removeChild(iframe);
        }
      }
    };
  }, []);
  /**
   * 监听浏览器刷新动作，如果处于acw状态需要关闭
   */
  useEffect(() => {
    let beforeUnloadHandler; // 用于存储事件处理函数的引用
    // if (allowReload) {
    //   window.removeEventListener('beforeunload', beforeUnloadHandler);
    // }
    if (
      ['callIn', 'callOut', 'calling', 'videoCalling', 'multiple'].includes(
        callingShow,
      )
      // &&!allowReload
    ) {
      beforeUnloadHandler = event => {
        event.preventDefault(); // 防止默认行为
        localStorage.setItem('callLog', JSON.stringify(callList));
        localStorage.setItem('callingStatus', JSON.stringify(callingStatus));
        // showConfirm();
      }; // 用于存储事件处理函数的引用
      window.addEventListener('beforeunload', beforeUnloadHandler);
    } else if (['callACW', 'videoACW'].includes(callingShow)) {
      beforeUnloadHandler = closeACW; // 注意这里假设closeACW函数是存在的
      window.addEventListener('beforeunload', beforeUnloadHandler);
    } else if (['initial'].includes(callingShow)) {
      // && !allowReload
      localStorage.setItem('callLog', JSON.stringify(''));
      localStorage.setItem('callingStatus', JSON.stringify(''));
    }

    // 清理事件监听器（确保传入的函数引用与添加时相同）
    return () => {
      window.removeEventListener('beforeunload', beforeUnloadHandler); // 使用之前存储的引用移除监听器
    };
  }, [callingShow, callList, callingStatus, allowReload]); // 根据需要调整依赖数组

  // const showConfirm = () => {
  //   confirm({
  //     title: 'Warning',
  //     icon: <ExclamationCircleFilled />,
  //     content: getIntl().formatMessage({
  //       id: 'new.worktable.phone.create.fail.refresh',
  //     }),
  //     onOk() {
  //       setAllowReload(true);

  //     },
  //     onCancel() {},
  //   });
  // };
  // useEffect(() => {
  //   if (allowReload) {
  //     window.location.reload();
  //   }
  // }, [allowReload]);
  /**
   * 判断数据是不是JSON
   */
  const isValidJson = (content, item) => {
    if (typeof content === 'string') {
      try {
        // 检查是否为数字字符串
        if (!isNaN(content) && content.trim() !== '') {
          return false;
        }
        let flag = JSON.parse(content);
        return flag; // 解析成功，说明是有效 JSON
      } catch (e) {
        return false; // 解析失败，说明不是有效 JSON
      }
    } else if (typeof content === 'object') {
      // 已经是对象，直接使用
      return content;
    }
    return false; // 不是字符串，返回 false
  };
  /**
   * 电话tab下的函数=====================================================================================================================
   */
  //获取路径
  const getLoginUrl = payload => {
    if (payload.connectId) {
      dispatch({
        type: 'worktable/getSaml2',
        payload: payload,
        callback: response => {
          if (response.code === 200) {
            let loginUrl = response.data;
            setLoginUrl(loginUrl.replace(/\+/g, '%2B'));
          }
        },
      });
    }
  };
  const syncCallRecord = payload => {
    dispatch({
      type: 'worktable/syncCallRecord',
      payload: payload,
    });
  };
  const handleKeyDown = (event, conn) => {
    const validKeys = [
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '*',
      '#',
    ];

    if (validKeys.includes(event.key)) {
      conn.sendDigits(event.key, {
        success: function() {
          console.log('触发按键成功====', event.key);
        },
        failure: function(err) {
          console.log('触发按键失败====', event.key);
        },
      });
    }
  };
  //获取座席列表
  const listQueueQuickConnects = value => {
    try {
      setQuickConnectLoading(true);
      let agent = new connect.Agent();
      agent.getEndpoints(agent.getAllQueueARNs(), {
        success: function(data) {
          setQuickConnectLoading(false);
          console.log(
            'valid_queue_phone_agent_endpoints',
            data.endpoints,
            'You can transfer the call to any of these endpoints',
          );
          setConnectUserList(data.endpoints);
          setFilteredData(data.endpoints);
        },
        failure: function() {
          console.log('setCcpError5');
          setCcpError(true);
        },
      });
    } catch (error) {
      notification.error({
        message: 'The agent is not yet initialized!',
      });
    }
  };
  //获取国旗列表
  const systemRegionTimezones = value => {
    dispatch({
      type: 'worktable/systemRegionTimezones',
      // type: 'worktable/queryCompanyTelPrefix',
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          const countries = {
            AF: 'Afghanistan',
            AL: 'Albania',
            DZ: 'Algeria',
            AS: 'American Samoa',
            AD: 'Andorra',
            AO: 'Angola',
            AI: 'Anguilla',
            AG: 'Antigua and Barbuda',
            AR: 'Argentina',
            AM: 'Armenia',
            AW: 'Aruba',
            AU: 'Australia',
            AT: 'Austria',
            AZ: 'Azerbaijan',
            BS: 'Bahamas',
            BH: 'Bahrain',
            BD: 'Bangladesh',
            BB: 'Barbados',
            BY: 'Belarus',
            BE: 'Belgium',
            BZ: 'Belize',
            BJ: 'Benin',
            BM: 'Bermuda',
            BT: 'Bhutan',
            BO: 'Bolivia, Plurinational State of',
            BA: 'Bosnia and Herzegovina',
            BW: 'Botswana',
            BR: 'Brazil',
            IO: 'British Indian Ocean Territory',
            BG: 'Bulgaria',
            BF: 'Burkina Faso',
            BI: 'Burundi',
            KH: 'Cambodia',
            CM: 'Cameroon',
            CA: 'Canada',
            CV: 'Cape Verde',
            KY: 'Cayman Islands',
            CF: 'Central African Republic',
            TD: 'Chad',
            CL: 'Chile',
            CN: 'China',
            CO: 'Colombia',
            KM: 'Comoros',
            CG: 'Congo',
            CD: 'Democratic Republic of the Congo',
            CK: 'Cook Islands',
            CR: 'Costa Rica',
            CI: "Côte d'Ivoire",
            HR: 'Croatia',
            CU: 'Cuba',
            CW: 'Curaçao',
            CY: 'Cyprus',
            CZ: 'Czech Republic',
            DK: 'Denmark',
            DJ: 'Djibouti',
            DM: 'Dominica',
            DO: 'Dominican Republic',
            EC: 'Ecuador',
            EG: 'Egypt',
            SV: 'El Salvador',
            GQ: 'Equatorial Guinea',
            ER: 'Eritrea',
            EE: 'Estonia',
            ET: 'Ethiopia',
            FK: 'Falkland Islands (Malvinas)',
            FO: 'Faroe Islands',
            FJ: 'Fiji',
            FI: 'Finland',
            FR: 'France',
            PF: 'French Polynesia',
            GA: 'Gabon',
            GM: 'Gambia',
            GE: 'Georgia',
            DE: 'Germany',
            GH: 'Ghana',
            GI: 'Gibraltar',
            GR: 'Greece',
            GL: 'Greenland',
            GD: 'Grenada',
            GU: 'Guam',
            GT: 'Guatemala',
            GG: 'Guernsey',
            GN: 'Guinea',
            GW: 'Guinea-Bissau',
            HT: 'Haiti',
            HN: 'Honduras',
            HK: 'Hong Kong',
            HU: 'Hungary',
            IS: 'Iceland',
            IN: 'India',
            ID: 'Indonesia',
            IR: 'Iran, Islamic Republic of',
            IQ: 'Iraq',
            IE: 'Ireland',
            IM: 'Isle of Man',
            IL: 'Israel',
            IT: 'Italy',
            JM: 'Jamaica',
            JP: 'Japan',
            JE: 'Jersey',
            JO: 'Jordan',
            KZ: 'Kazakhstan',
            KE: 'Kenya',
            KI: 'Kiribati',
            KP: 'North Korea',
            KR: 'South Korea',
            KW: 'Kuwait',
            KG: 'Kyrgyzstan',
            LA: "Lao People's Democratic Republic",
            LV: 'Latvia',
            LB: 'Lebanon',
            LS: 'Lesotho',
            LR: 'Liberia',
            LY: 'Libya',
            LI: 'Liechtenstein',
            LT: 'Lithuania',
            LU: 'Luxembourg',
            MO: 'Macao',
            MK: 'Republic of Macedonia',
            MG: 'Madagascar',
            MW: 'Malawi',
            MY: 'Malaysia',
            MV: 'Maldives',
            ML: 'Mali',
            MT: 'Malta',
            MH: 'Marshall Islands',
            MQ: 'Martinique',
            MR: 'Mauritania',
            MU: 'Mauritius',
            MX: 'Mexico',
            FM: 'Micronesia, Federated States of',
            MD: 'Republic of Moldova',
            MC: 'Monaco',
            MN: 'Mongolia',
            ME: 'Montenegro',
            MS: 'Montserrat',
            MA: 'Morocco',
            MZ: 'Mozambique',
            MM: 'Myanmar',
            NA: 'Namibia',
            NR: 'Nauru',
            NP: 'Nepal',
            NL: 'Netherlands',
            NZ: 'New Zealand',
            NI: 'Nicaragua',
            NE: 'Niger',
            NG: 'Nigeria',
            NU: 'Niue',
            NF: 'Norfolk Island',
            MP: 'Northern Mariana Islands',
            NO: 'Norway',
            OM: 'Oman',
            PK: 'Pakistan',
            PW: 'Palau',
            PS: 'Palestinian Territory',
            PA: 'Panama',
            PG: 'Papua New Guinea',
            PY: 'Paraguay',
            PE: 'Peru',
            PH: 'Philippines',
            PN: 'Pitcairn',
            PL: 'Poland',
            PT: 'Portugal',
            PR: 'Puerto Rico',
            QA: 'Qatar',
            RO: 'Romania',
            RU: 'Russia',
            RW: 'Rwanda',
            KN: 'Saint Kitts and Nevis',
            LC: 'Saint Lucia',
            WS: 'Samoa',
            SM: 'San Marino',
            ST: 'Sao Tome and Principe',
            SA: 'Saudi Arabia',
            SN: 'Senegal',
            RS: 'Serbia',
            SC: 'Seychelles',
            SL: 'Sierra Leone',
            SG: 'Singapore',
            SX: 'Sint Maarten',
            SK: 'Slovakia',
            SI: 'Slovenia',
            SB: 'Solomon Islands',
            SO: 'Somalia',
            ZA: 'South Africa',
            SS: 'South Sudan',
            ES: 'Spain',
            LK: 'Sri Lanka',
            SD: 'Sudan',
            SR: 'Suriname',
            SZ: 'Swaziland',
            SE: 'Sweden',
            CH: 'Switzerland',
            SY: 'Syria',
            TW: 'Taiwan',
            TJ: 'Tajikistan',
            TZ: 'Tanzania',
            TH: 'Thailand',
            TG: 'Togo',
            TK: 'Tokelau',
            TO: 'Tonga',
            TT: 'Trinidad and Tobago',
            TN: 'Tunisia',
            TR: 'Turkey',
            TM: 'Turkmenistan',
            TC: 'Turks and Caicos Islands',
            TV: 'Tuvalu',
            UG: 'Uganda',
            UA: 'Ukraine',
            AE: 'United Arab Emirates',
            GB: 'United Kingdom',
            // US: 'United States',
            US: 'America/Canada',
            UY: 'Uruguay',
            UZ: 'Uzbekistan',
            VU: 'Vanuatu',
            VE: 'Venezuela, Bolivarian Republic of',
            VN: 'Viet Nam',
            VI: 'Virgin Islands',
            YE: 'Yemen',
            ZM: 'Zambia',
            ZW: 'Zimbabwe',
          };
          // 将键的第二个字母转换为小写
          const updatedCountries = Object.fromEntries(
            Object.entries(countries)
              .map(([key, value]) => [
                key[0] + key[1].toLowerCase() + key.slice(2), // 小写第二个字母
                value,
              ])
              .map(([key, value]) => [value, key]), // 反转键值
          );
          console.log(updatedCountries, '国旗列表');
          let regionList = data?.map(item => {
            return {
              svgIcon: updatedCountries[item.countryEn],
              ...item,
            };
          });
          console.log(regionList, callList, '整合后的国旗select');
          setRegionTimezones(regionList);

          setCallList(preState => {
            const tempCallList = { ...preState }; // 创建callList的副本
            tempCallList.userList[0].keyNumberArea =
              regionList?.[0]?.telephonePrefix;
            return tempCallList;
          });
        }
      },
    });
  };
  //修改实例
  const onChangeConncet = (value, item) => {
    handleCallList('connectId', value);
    getLoginUrl({
      connectId: value,
    });
    // 暂存到redux中
    dispatch({
      type: 'layouts/updateConnect',
      payload: item.option,
    });
    // 存到Redis中
    dispatch({
      type: 'layouts/saveSelectedConnect',
      payload: item.option,
    });
  };
  //这里需要拿到awsAccountId，queueArn,queueName
  const getQueuesList = routingProfile => {
    //"arn:aws:connect:<REGION>:<ACCOUNT_ID>:instance/<CONNECT_INSTANCE_ID>/queue/<CONNECT_QUEUE_ID>";
    let splits = routingProfile.queues[0].queueARN?.split(':');
    let awsAccountId =
      splits[4] && splits[5].includes('instance') ? splits[4] : '';
    let qs = {
      queueName: routingProfile.queues[0].name,
      queueArn: routingProfile.queues[0].queueARN,
      awsAccountId,
    };
    setQueues(qs);
    return qs;
  };
  //初始化通话ccp
  const init = (selectedConnect, ssoUrl) => {
    let queuesTemp = {};
    setCcpLoading(true);
    console.log(
      'selectedConnect=====',
      selectedConnect,
      ',connectUrl = ===',
      selectedConnect.connectUrl,
      ',',
      selectedConnect.regionCode,
      'loginUrl=====' + ssoUrl,
    );
    const connectUrl = selectedConnect.connectUrl;
    const regionCode = selectedConnect.regionCode;
    let data = {
      style: 'width:100%; height:100%;border:none',
      // ccpUrl: connectUrl + '/ccp-v2/',
      ccpParams: {
        loginUrl: ssoUrl,
        loginPopup: true, // optional, defaults to `true`
        loginPopupAutoClose: true, // optional, defaults to `false`
        loginOptions: {
          // optional, if provided opens login in new window
          autoClose: true, // optional, defaults to `false`
          height: 600, // optional, defaults to 578
          width: 400, // optional, defaults to 433
          top: 0, // optional, defaults to 0
          left: 0, // optional, defaults to 0
        },
        softphone: {
          // optional, defaults below apply if not provided
          allowFramedSoftphone: true, // optional, defaults to false
          disableRingtone: false, // optional, defaults to false
          // ringtoneUrl: "./ringtone.mp3" // optional, defaults to CCP’s default ringtone if a falsy value is set
          allowFramedVideoCall: true, // 开启视频通话
          allowEarlyGum: true, // 提前加载音频模块
        },
        pageOptions: {
          //optional
          enableVideoDeviceSettings: true, // 开启视频功能
          enableAudioDeviceSettings: true, // 开启音频功能
          enablePhoneTypeSettings: true, //optional, defaults to 'true'
        },
        shouldAddNamespaceToLogs: true, //optional, defaults to 'false'
        ccpAckTimeout: 5000, //optional, defaults to 3000 (ms)
        ccpSynTimeout: 3000, //optional, defaults to 1000 (ms)
        ccpLoadTimeout: 2000, //optional, defaults to 5000 (ms)
        region: regionCode, // 把这个region加上，现在接口没有
      },
    };

    if (!ssoUrl) {
      delete data.ccpParams.loginUrl;
    }
    connect.agentApp.initApp(
      'ccp',
      'container-div',
      connectUrl + '/ccp-v2/',
      data,
    );
    console.log('ccp参数=====', data, connect);
    // 登录成功回调
    connect.core.onInitialized(() => {
      setCcpLoading(false);
      setCcpError(false);
    });
    connect.agent(agent => {
      //初始化拿到queues
      let routingProfile = agent.getRoutingProfile();
      queuesTemp = getQueuesList(routingProfile);
      console.log(
        agent.getConfiguration(),
        queuesTemp,
        'agent.getConfiguration',
      );
      agent.onError(error => {
        let err = isValidJson(error) ? JSON.parse(error) : error;
        notification.error({
          message: err?.message,
        });
        console.log(error, '触发agent===error===========================');
      });
      agent.onSoftphoneError(error => {
        console.log('触发Error type: ', error.errorType);
        console.log('触发Error message: ', error.errorMessage);
        console.log('触发Error endpoint url: ', error.endPointUrl);
        setAlertShow(true);
        setAlertText(error.errorMessage);
      });
      agent.onLocalMediaStreamCreated(data => {
        console.log('触发local media stream created for connection: ', data);
      });
      agent.onMuteToggle(obj => {
        console.log('触发onMuteToggle', obj);
      });
      agent.onSpeakerDeviceChanged(obj => {
        console.log('触发onSpeakerDeviceChanged', obj);
      });
      agent.onMicrophoneDeviceChanged(obj => {
        console.log('触发onMicrophoneDeviceChanged', obj);
      });
      agent.onRingerDeviceChanged(obj => {
        console.log('触发onRingerDeviceChanged', obj);
      });
      agent.onCameraDeviceChanged(obj => {
        console.log('触发onCameraDeviceChanged', obj);
      });
    });

    // 联系人回调
    connect.contact(contact => {
      //记录呼入呼出状态，false呼出，true呼入
      let isInbound = contact.isInbound();
      //第一个联系人的connection
      let initialConnection = contact.getInitialConnection();
      //会话中联系人列表
      let connectionList = contact.getConnections();
      //存入整个通话唯一contactId
      handleCallList('contactId', contact.getContactId());
      //判断是否视频通话
      let hasVideoRTCCapabilities = contact.hasVideoRTCCapabilities();
      setVideoRTCCapabilities(hasVideoRTCCapabilities);
      //视频来电信息
      let name = '';
      let dieTelefonnummer = '';
      if (
        contact.getAttributes() &&
        Object.keys(contact.getAttributes()).length > 0
      ) {
        name = contact.getAttributes().chat_user_name?.value;
      }
      setVideoAttributes(contact.getAttributes());

      console.log(
        '触发=======',
        contact,
        initialConnection,
        contact.isInbound(),
        contact.isSoftphoneCall(),
        contact.isConnected(),
        contact.getContactId(),
        contact.getInitialConnection(),
        contact.getConnections(),
        contact.getInitialContactId(),
        contact.getAgentConnection(),
        initialConnection.isActive(),
        initialConnection.isOnHold(),
        '触发视频================',
        contact.getAttributes(),
        contact.hasVideoRTCCapabilities(),
        contact.canAgentSendVideo(),
        contact.canAgentReceiveVideo(),
        contact.isMultiPartyConferenceEnabled(),
        contact.getAttributes(),
      );

      // 获取联系人语言代码并找到对应的语言标签
      let languageCode = contact.getAttributes()?.language_code?.value;
      let languageLabel = '';
      console.log(
        'languageCode && googleLanguage',
        languageCode,
        googleLanguage,
      );
      if (languageCode && googleLanguage) {
        // 在googleLanguage中查找匹配的语言
        const languageObj = googleLanguage.find(
          item => item.value === languageCode,
        );
        if (languageObj) {
          languageLabel = languageObj.label;
          setContactLanguageLabel(languageLabel);
          console.log('找到语言标签:', languageLabel);
        } else {
          console.log('未找到语言代码对应的语言标签:', languageCode);
        }
      }

      // 订阅当联系待处理时要调用的方法。该事件预计在连接事件之前发生。
      contact.onConnecting(connecting => {
        //非视频下，呼入呼出状态下，记录来电号码和状态
        if (!hasVideoRTCCapabilities) {
          if (isInbound) {
            const phoneNumber = parsePhoneNumber(
              initialConnection?.getEndpoint()?.phoneNumber,
            ); // 输入号码
            dieTelefonnummer =
              '+' +
              String(phoneNumber.countryCallingCode) +
              phoneNumber.nationalNumber;
            //呼入记录呼入状态，呼叫方电话号码
            handleCallList('userList', [
              '',
              [
                ['keyNumber', phoneNumber.nationalNumber],
                ['keyNumberArea', '+' + String(phoneNumber.countryCallingCode)],
                ['status', 'callIn'],
              ],
            ]);
            sessionStorage.setItem(
              'AfterPhoneNumber',
              phoneNumber.nationalNumber,
            );
            sessionStorage.setItem(
              'AfterKeyNumberArea',
              '+' + String(phoneNumber.countryCallingCode),
            );
            callingShowChange('callIn');
            addPhoneContactDetail({
              // workOrderId: '', //工单id,在Connected中获取
              contactId: contact.getContactId(),
              instanceId: callList.connectId,
              awsAccountId: queuesTemp.awsAccountId, // AWS 账号ID
              queueArn: queuesTemp.queueArn, // 队列ARN
              queueName: queuesTemp.queueName, // 队列名称
              connectAlias: connectList.find(
                c => c.value === callList.connectId,
              ).label, // 实例别名（联络线路)
              callChannel: 'VOICE', // 渠道 voice-电话，chat-聊天，email-邮件
              channelTypeId: '7', // 渠道类型 7-电话
              eventType: '1', //1、响铃 2、接听 3、呼出 4、ACW事件 5、挂断 6、暂停 7、转接
              ringTime: moment()
                .utc()
                .add(8, 'hours')
                .format('YYYY-MM-DD HH:mm:ss'), // 开始时间(UTC+8)
            });
            // let title = getIntl().formatMessage({
            //   id: 'worktable.notification.call.title',
            // });
            // let options = {
            //   dir: 'auto', // 文字方向
            //   body: getIntl().formatMessage({
            //     id: 'worktable.notification.call.content',
            //     number: phoneNumber,
            //   }), // 通知主体
            //   requireInteraction: false, // 不自动关闭通知
            //   // 通知图标
            //   icon:
            //     'https://connectnow-demo-html-prod.s3.ap-southeast-1.amazonaws.com/ConnectNow.ico',
            // };
            // notifyMe(title, options);
          } else {
            addPhoneContactDetail({
              // workOrderId: '', //工单id,在Connected中获取
              contactId: contact.getContactId(),
              instanceId: callList.connectId,
              awsAccountId: queuesTemp.awsAccountId, // AWS 账号ID
              queueArn: queuesTemp.queueArn, // 队列ARN
              queueName: queuesTemp.queueName, // 队列名称
              connectAlias: connectList.find(
                c => c.value === callList.connectId,
              ).label, // 实例别名（联络线路)
              callChannel: 'VOICE', // 渠道 voice-电话，chat-聊天，email-邮件
              channelTypeId: '7', // 渠道类型 7-电话
              eventType: '3', //1、响铃 2、接听 3、呼出 4、ACW事件 5、挂断 6、暂停 7、转接
              startTime: moment()
                .utc()
                .add(8, 'hours')
                .format('YYYY-MM-DD HH:mm:ss'), // 开始时间(UTC+8)
              // 呼入时间（座席应答时间）在Connected中获取
              //  callTime:
              customerPhone:
                callList.userList[0].keyNumberArea +
                callList.userList[0].keyNumber, // 客户电话
              //  systemPhone:拿不到,// 系统电话
            });
            //进入呼出状态
            callingShowChange('callOut');
            handleCallList('userList', [
              connectionList[connectionList?.length - 1].connectionId,
              [['status', 'callOut']],
            ]);
          }
        } else {
          dispatch({
            type: 'worktable/queryChannelById',
            payload: { channelId: contact.getAttributes().channelId?.value },
            callback: response => {
              let { code, data, msg } = response;
              if (code === 200) {
                // 在线聊天的视频工单 10-web端视频 11-app端视频
                let channelTypeId = data.channelType;
                dieTelefonnummer = contact.getAttributes().chat_phone_number
                  ?.value;
                addPhoneContactDetail({
                  // workOrderId: '', //工单id,在Connected中获取
                  contactId: contact.getContactId(),
                  instanceId: callList.connectId,
                  awsAccountId: queuesTemp.awsAccountId, // AWS 账号ID
                  queueArn: queuesTemp.queueArn, // 队列ARN
                  queueName: queuesTemp.queueName, // 队列名称
                  connectAlias: connectList.find(
                    c => c.value === callList.connectId,
                  ).label, // 实例别名（联络线路)
                  callChannel: 'VOICE', // 渠道 voice-电话，chat-聊天，email-邮件
                  channelTypeId: channelTypeId, // 渠道类型 17-web语音
                  eventType: '1', //1、响铃 2、接听 3、呼出 4、ACW事件 5、挂断 6、暂停 7、转接
                  ringTime: moment()
                    .utc()
                    .add(8, 'hours')
                    .format('YYYY-MM-DD HH:mm:ss'), // 开始时间(UTC+8)
                });
              }
            },
          });

          //呼入记录呼入状态，呼叫方电话号码
          handleCallList('userList', [
            '',
            [
              ['name', name],
              ['status', 'videoCallin'],
            ],
          ]);
          callingShowChange('videoCallin');
          // let title = getIntl().formatMessage({
          //   id: 'worktable.notification.call.title',
          // });
          // let options = {
          //   dir: 'auto', // 文字方向
          //   body: getIntl().formatMessage({
          //     id: 'worktable.notification.call.content',
          //     number: name,
          //   }), // 通知主体
          //   requireInteraction: false, // 不自动关闭通知
          //   // 通知图标
          //   icon:
          //     'https://connectnow-demo-html-prod.s3.ap-southeast-1.amazonaws.com/ConnectNow.ico',
          // };
          // notifyMe(title, options);
        }
        //监听按键
        window.addEventListener('keydown', e =>
          handleKeyDown(e, initialConnection),
        );
        console.log(
          connecting,
          '触发connecting===========================addPhoneContactDetail',
        );
      });
      // 有联系人接入时回调 生成工单，渲染邮件详情页面。
      contact.onConnected(connected => {
        //先检查是否存在通话中刷新的情况
        if (isValidJson(localStorage.getItem('callLog')) && callLogsExist) {
          setCallList(isValidJson(localStorage.getItem('callLog')));
          setCallingStatus(isValidJson(localStorage.getItem('callingStatus')));
          callingShowChange(
            isValidJson(localStorage.getItem('callLog'))?.userList[0]?.status,
          );
        } else {
          //呼入呼出状态下，接受通话
          if (isInbound && !hasVideoRTCCapabilities) {
            //呼入成功记录connectionId，通话状态
            handleCallList('userList', [
              '',
              [
                [
                  'connectionId',
                  connectionList[connectionList?.length - 1].connectionId,
                ],
                ['status', 'calling'],
              ],
            ]);
            callingShowChange('calling');
            let payload = {
              connectId: callList.connectId,
              channelTypeId: '7', //7：电话
              contactId: contact.getContactId(),
              chatVoice: 0,
              channelId: '',
              // customerContactInfo: contact.getConnections()[1].getAddress()
              //   .phoneNumber,
              customerContactInfo: initialConnection?.getEndpoint()
                ?.phoneNumber,
              sendingMail: 0,
              extendedAttribute: JSON.stringify(contact.getAttributes()),
              callType: 'INBOUND',
              ticketLanguage: contact.getAttributes().language_code?.value,
            };
            //自动创建工单
            autoCreateWork(payload, {
              contactId: contact.getContactId(),
              instanceId: callList.connectId,
              eventType: '8', //1、响铃 2、接听 3、呼出 4、ACW事件 5、挂断 6、暂停 7、转接
            });
          } else if (hasVideoRTCCapabilities) {
            //视频呼入成功记录connectionId，通话状态
            handleCallList('userList', [
              '',
              [
                [
                  'connectionId',
                  connectionList[connectionList?.length - 1].connectionId,
                ],
                ['status', 'videoCalling'],
              ],
            ]);
            callingShowChange('videoCalling');

            dispatch({
              type: 'worktable/queryChannelById',
              payload: { channelId: contact.getAttributes().channelId?.value },
              callback: response => {
                let { code, data, msg } = response;
                if (code === 200) {
                  // 在线聊天的视频工单 10-web端视频 11-app端视频
                  let payload = {
                    connectId: callList.connectId,
                    channelTypeId: data.channelType,
                    contactId: contact.getContactId(),
                    chatVoice: 1,
                    channelId: contact.getAttributes().channelId.value,
                    customerContactInfo: contact.getAttributes().chat_email
                      ?.value,
                    chatUserName: contact.getAttributes().chat_user_name.value,
                    //自动创建工单ributes().chat_user_name?.value,
                    phoneNumber: contact.getAttributes().chat_phone_number
                      ?.value,
                    sendingMail: 0,
                    extendedAttribute: JSON.stringify(contact.getAttributes()),
                    callType: 'INBOUND',
                    ticketLanguage: contact.getAttributes().language_code
                      ?.value,
                  };
                  autoCreateWork(payload, {
                    contactId: contact.getContactId(),
                    instanceId: callList.connectId,
                    eventType: '8', //1、响铃 2、接听 3、呼出 4、ACW事件 5、挂断 6、暂停 7、转接 8、补充缺失参数
                  });
                }
              },
            });
          } else {
            //呼出成功记录connectionId，通话状态
            handleCallList('userList', [
              '',
              [
                [
                  'connectionId',
                  connectionList[connectionList?.length - 1].connectionId,
                ],
                ['status', 'calling'],
              ],
            ]);

            callingShowChange('calling');
            let payload = {
              connectId: callList.connectId,
              channelTypeId: '7', //7：电话
              contactId: contact.getContactId(),
              chatVoice: 0,
              channelId: '',
              // customerContactInfo: contact.getConnections()[1].getAddress()
              //   .phoneNumber,
              customerContactInfo: initialConnection?.getEndpoint()
                ?.phoneNumber,
              sendingMail: 0,
              callType: 'OUTBOUND',
              ticketLanguage: contact.getAttributes().language_code?.value,
            };
            let callTime = moment()
              .utc()
              .add(8, 'hours')
              .format('YYYY-MM-DD HH:mm:ss');
            //自动创建工单
            autoCreateWork(payload, {
              contactId: contact.getContactId(),
              instanceId: callList.connectId,
              eventType: '8', //1、响铃 2、接听 3、呼出 4、ACW事件 5、挂断 6、暂停 7、转接
              // 呼入时间（座席应答时间）这是呼出补全的数据
              callTime: callTime,
            });
          }
        }

        console.log(
          connected,
          '触发connected===========================addPhoneContactDetail',
        );
      });
      // 接受联系人
      contact.onAccepted(async accepted => {
        addPhoneContactDetail({
          workOrderId: '', //工单id,在Connected中获取
          contactId: contact.getContactId(),
          instanceId: callList.connectId,
          callTime: moment()
            .utc()
            .add(8, 'hours')
            .format('YYYY-MM-DD HH:mm:ss'),
          eventType: '2', //1、响铃 2、接听 3、呼出 4、ACW事件 5、挂断 6、暂停 7、转接
          customerPhone: dieTelefonnummer, // 客户电话
          //  systemPhone:拿不到,// 系统电话
        });
        console.log(
          accepted,
          hasVideoRTCCapabilities,
          '触发accepted===========================addPhoneContactDetail',
        );
      });
      // 结束任务时回调
      contact.onDestroy(connectDestroy => {
        setAlertShow(false);
        if (!isInbound) {
          //呼出后，客户未接通，主动取消呼叫
          callingShowChange('initial');
        } else {
          callingShowChange('initial');
        }
        sessionStorage.setItem('autoCreateWorkID', ''),
          console.log(
            connectDestroy,
            '触发connectDestroy===========================',
          );
        sessionStorage.setItem('AfterPhoneNumber', '');
        sessionStorage.setItem('AfterKeyNumberArea', '');
      });

      contact.onRefresh(refresh => {
        connectionList = contact?.getConnections();
        //当加入第三方通话时检查他们的onHold状态
        if (refresh.getSingleActiveThirdPartyConnection()) {
          //存储新加入的connection,只有最新的userLIst的connectionId为‘’，所以赋值的时候需要传入‘’对应才能找到
          if (refresh.getSingleActiveThirdPartyConnection().isConnected()) {
            console.log(
              refresh.getSingleActiveThirdPartyConnection().connectionId,
              callList.userList,
              'connectionListconnectionList',
            );
            handleCallList('userList', [
              '',
              [
                [
                  'connectionId',
                  refresh.getSingleActiveThirdPartyConnection().connectionId,
                ],
                ['status', 'calling'],
              ],
            ]);
          }
          //ccp面板在新加入联系人时，会将以存在的联系人置于hold状态
          connectionList?.forEach(item => {
            if (item.isOnHold()) {
              handleCallList('userList', [
                item.connectionId,
                [['callingStatus', 'keep', true]],
              ]);
            }
          });
        }
        //查找非连接状态的会话，踢出会议
        setCallList(preState => {
          let newData = preState;
          if (preState.userList?.length > 1) {
            connectionList?.forEach(item => {
              // console.log('触发newData0=====', item.getState());
              if (item.getState().type === 'disconnected') {
                let i = preState?.userList?.findIndex(
                  it => it.connectionId === item.connectionId,
                );
                if (i != -1) {
                  newData?.userList?.splice(i, 1);
                }
              }
            });
          }
          return newData;
        });
        console.log(
          '触发refresh===========================',
          // callList,
          // refresh,
          // initialConnection.isActive(),
          // initialConnection.isOnHold(),
          // contact.getContactId(),
          // contact.getInitialConnection(),
          // contact.conferenceConnections(),
          // contact.getConnections(),
          // contact.getActiveInitialConnection(),
          // refresh.getAgentConnection(),
          // contact.getThirdPartyConnections(),
          // refresh.getSingleActiveThirdPartyConnection(),
          // '触发视频================',
          // contact.getAttributes(),
          // contact.hasVideoRTCCapabilities(),
          // contact.canAgentSendVideo(),
          // contact.canAgentReceiveVideo(),
          // contact.isMultiPartyConferenceEnabled(),
          // initialConnection.getMediaType(),
          // initialConnection.canSendVideo(),
        );
      });
      contact.onError(error => {
        let err = isValidJson(error) ? JSON.parse(error) : error;
        notification.error({
          message: err?.message,
        });
        console.log(error, '触发contact===error===========================');
      });
      contact.onACW(acw => {
        setAlertShow(false);
        console.log(
          acw,
          acw.getInitialConnection(),
          acw.getConnections(),
          sessionStorage.getItem('autoCreateWorkID'),
          '触发acw===========================',
        );
        //这里电话和livechat都是公用的
        addPhoneContactDetail({
          workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,在Connected中获取
          contactId: contact.getContactId(),
          instanceId: callList.connectId,
          eventType: '5', //1、响铃 2、接听 3、呼出 4、ACW事件 5、挂断 6、暂停 7、转接
          endTime: moment()
            .utc()
            .add(8, 'hours')
            .format('YYYY-MM-DD HH:mm:ss'), // 挂断时间
          hangingType: '2', // 挂断类型 1、坐席结束  2、客户结束
        });
        addPhoneContactDetail({
          workOrderId: sessionStorage.getItem('autoCreateWorkID'), //工单id,在Connected中获取
          contactId: contact.getContactId(),
          instanceId: callList.connectId,
          eventType: '4', //1、响铃 2、接听 3、呼出 4、ACW事件 5、挂断 6、暂停 7、转接
          acwTime: moment()
            .utc()
            .add(8, 'hours')
            .format('YYYY-MM-DD HH:mm:ss'), // 开始时间(UTC+8)
        });
        if (hasVideoRTCCapabilities) {
          callingShowChange('videoACW');
        } else {
          callingShowChange('callACW');
          //重置一下chat呼叫过来的状态
          setChatPhone('');
          setChatPhoneType(false);
        }
        // 如果是来源是电话 通知后台发送mq，获取通话记录
        const payload = {
          contactId: contact.getContactId(),
          ticketLanguage: contact.getAttributes().languageCode?.value,
        };
        syncCallRecord(payload);
      });
      contact.onIncoming(incoming => {
        console.log(incoming, '触发incoming===========================');
      });
      contact.onMissed(missed => {
        //处理当被呼叫未应答时，ccp20秒后会进入miss状态，自研需要清除该状态，持续被呼叫状态
        if (isInbound || hasVideoRTCCapabilities) {
          contact?.clear({
            success: function() {
              console.log('触发清除呼入======');
            },
            failure: function(err) {
              callingShowChange('initial');
              console.log('触发清除呼入error======', err);
            },
          });
        }
        console.log(missed, '触发missed===========================');
      });
      contact.onEnded(ended => {
        console.log(
          ended,

          '触发ended===========================',
        );
      });
      contact.onPending(padding => {
        console.log(
          padding,

          '触发padding===========================',
        );
      });
    });
  };
  /**
   * 更新通话列表数据
   */
  const handleCallList = (key, value) => {
    if (!key) {
      return;
    }
    setCallList(preState => {
      const callListCopy = { ...preState }; // 创建callList的副本
      console.log(key, value, callListCopy, 'handleCallList');
      if (key === 'userList') {
        const foundIndex = callListCopy.userList?.findIndex(
          item => item.connectionId === value[0],
        ); // 根据id查找对应的对象
        if (foundIndex != -1) {
          value[1]?.forEach(([key, value, flag]) => {
            //单独处理计时器，需要前值+1
            if (key === 'timeKeeping') {
              callListCopy.userList[foundIndex][key] =
                value === 0 ? 0 : callListCopy.userList[foundIndex][key] + 1;
            } else if (key === 'callingStatus') {
              //单独处理媒体状态，需要可以存储多个值
              let newStatusArray = [
                ...new Set(callListCopy.userList[foundIndex][key]),
              ];
              console.log(newStatusArray, flag, value, '更新callingStatus');
              if (flag) {
                newStatusArray?.push(value);
              } else {
                let i = newStatusArray?.findIndex(item => value === item);
                if (i != -1) {
                  newStatusArray?.splice(i, 1);
                }
              }
              callListCopy.userList[foundIndex][key] = newStatusArray;
            } else {
              callListCopy.userList[foundIndex][key] = value; // 更新对应的 属性;
            }
          });
        }
      } else {
        callListCopy[key] = value;
      }
      return callListCopy;
    }); // 更新状态
  };
  /**
   * 更新通话中的状态
   */
  const handleCallStatus = (flag, value) => {
    setCallingStatus(preState => {
      const callStatusCopy = [...preState];
      if (flag) {
        callStatusCopy.push(value);
      } else {
        let i = callStatusCopy.findIndex(item => value === item);
        if (i != -1) {
          callStatusCopy.splice(i, 1);
        }
      }
      return callStatusCopy;
    });
  };
  /**
   * 预推入新的用户
   */
  const pushUserList = value => {
    const callListCopy = { ...callList };
    const newUserList = {
      connectionId: '',
      keyNumberArea: regionTimezones?.[0]?.telephonePrefix,
      keyNumber: '',
      timeKeeping: 0,
      role: '',
      callingStatus: [value],
      name: '',
    };
    callListCopy.userList.push(newUserList);
    setCallList(callListCopy);
  };
  /**
   * 取消操作删掉预推入的新用户
   */
  const deleteUserList = (index, value) => {
    setCallList(pre => {
      const callListCopy = { ...pre };
      callListCopy.userList.pop();
      return callListCopy;
    });
  };
  /**
   * 把电话号码绑定到最新的userList下
   */
  const handleKeyNumber = value => {
    setCallList(pre => {
      const callListCopy = { ...pre };
      callListCopy.userList[
        callListCopy.userList.length - 1
      ].keyNumber = value?.replace(/_/g, '');
      return callListCopy;
    });
  };
  /**
   * 回车事件
   */
  const onPressEnter = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      outBoundCall(
        callList.userList[callList.userList.length - 1].keyNumberArea +
          callList.userList[callList.userList.length - 1].keyNumber,
      );
    }
  };
  /**
   * 带有检索功能的手机号搜索框
   */
  const handleSearchKeyNumber = value => {
    setCallList(pre => {
      const callListCopy = { ...pre };
      callListCopy.userList[
        callListCopy.userList.length - 1
      ].name = value?.replace(/_/g, '');
      return callListCopy;
    });
    const searchTerm = value?.toLowerCase(); // 将输入转为小写进行不区分大小写的匹配
    const filtered = connectUserList?.filter(item =>
      item.name?.toLowerCase()?.includes(searchTerm),
    ); // 模糊匹配name属性
    setFilteredData(filtered); // 更新过滤后的数据数组状态
    if (filtered.length == 1) {
      setCurrentQCUser(filtered[0]);
    } else {
      setCurrentQCUser(null);
    }
  };
  // 按下键盘
  const passKey = (index, value) => {
    //拨号功能
    if (index === 0) {
      playAudio();
      let key = callList.userList[index].keyNumber + value;
      handleCallList('userList', ['', [['keyNumber', key]]]);
    }
    //数字键盘，通话中按键事件
    if (index === 1) {
      playAudio();
      setKeyboardValue(pre => {
        return pre + value;
      });
      let contact = new connect.Contact(callList.contactId);

      let connectionList = contact.getConnections();
      console.log(connectionList, contact, 'connectionLists数字键盘');
      connectionList[0]?.sendDigits(value, {
        success: function() {
          console.log('触发按键成功====', value);
        },
        failure: function(err) {
          console.log('触发按键失败====', value);
        },
      });
    }
  };
  //接听状态切换
  const callingShowChange = value => {
    setCallingShow(value);
  };
  // 用于格式化时间为 MM:SS
  const formatTime = secs => {
    const minutes = String(Math.floor(secs / 60)).padStart(2, '0');
    const seconds = String(secs % 60).padStart(2, '0');
    return `${minutes}:${seconds}`;
  };
  //格式化电话号码
  const formatKeyNumber = keyNumber => {
    // 找到所有数字并拼接成格式
    // const formatted = keyNumber
    //   ?.replace(/[^\d*]/g, '')
    //   ?.replace(/(\d{3})(\d{4})(\d+)/, '$1 $2 $3');
    let formattedNumBer = keyNumber
      ? keyNumber
      : sessionStorage.getItem('AfterPhoneNumber')
      ? sessionStorage.getItem('AfterPhoneNumber')
      : '';
    const formatted =
      formattedNumBer?.[0] +
      '******' +
      formattedNumBer?.[formattedNumBer.length - 1];
    return formatted;
  };
  const extractAreaCode = (phone1, phone2) => {
    // 将电话号字符串转为数组，并确保相同的部分只留下
    const commonPrefix = phone1
      .split('')
      .filter((char, index) => char === phone2[index])
      .join('');
    // 提取区号
    const areaCode = phone1.replace(commonPrefix, ''); // 取出多余部分
    return areaCode;
  };
  //处理直接复制带有区号的号码到输入框并拨打的情况
  const handleCountryCode = value => {
    let arrayList = value?.split('+');
    let phoneNumber = value;
    if (arrayList?.length > 0) {
      phoneNumber = '+' + arrayList[arrayList.length - 1];
    }
    console.log('处理后的电话号码', callList, phoneNumber, arrayList);
    const phoneNumberNew = parsePhoneNumber(phoneNumber); // 输入号码

    handleCallList('userList', [
      callList.userList[callList.userList.length - 1].connectionId,
      [
        ['keyNumber', phoneNumberNew?.nationalNumber],
        ['keyNumberArea', '+' + String(phoneNumberNew?.countryCallingCode)],
      ],
    ]);
    return phoneNumber;
  };
  //呼出
  const outBoundCall = value => {
    try {
      let number = handleCountryCode(value);
      let endpoint = connect.Endpoint.byPhoneNumber(number);
      let agent = new connect.Agent();
      let queueArns = agent.getAllQueueARNs();

      console.log(endpoint, queueArns, '触发单人端点====');
      if (queueArns.length >= 1) {
        agent.connect(endpoint, {
          queueARN: queueArns[0],
          success: function(res) {},
          failure: function(err) {
            console.log('outbound call connection failed');
            console.log(err);
            let error = isValidJson(err) ? JSON.parse(err) : err;
            notification.error({
              message: error?.message,
            });
          },
        });
      }
    } catch (error) {
      notification.error({
        message: 'The agent is not yet initialized!',
      });
    }
  };
  //数字键盘呼出，多人连接
  const multiConnectCall = target => {
    let agent = new connect.Agent();
    let contact = new connect.Contact(callList.contactId);
    let endpoint = null;
    if (target === 'qucikConnect' && currentQCUser) {
      /**
       * 点击坐席列表拨打
       * **/
      endpoint = currentQCUser;
      setCallList(pre => {
        const callListCopy = { ...pre };
        callListCopy.userList[callListCopy.userList.length - 1].name =
          endpoint.name;
        return callListCopy;
      });
    } else if (target === 'qucikConnect' && !currentQCUser) {
      /**
       * 在坐席列表检索不存在情况下使用电话号码拨打，校验只包含数字
       * **/
      if (
        /^\+?\d+$/.test(callList.userList[callList.userList.length - 1].name)
      ) {
        let number = handleCountryCode(
          callList?.userList?.[callList?.userList?.length - 1]?.keyNumberArea +
            callList?.userList?.[callList?.userList?.length - 1]?.name,
        );
        console.log(
          callList?.userList?.[callList?.userList?.length - 1]?.keyNumberArea +
            callList?.userList?.[callList?.userList?.length - 1]?.name,
          number,
          '需要处理的电话',
        );
        // setCallList(pre => {
        //   const callListCopy = { ...pre };
        //   callListCopy.userList[callListCopy.userList.length - 1].keyNumber =
        //     callList?.userList?.[callList?.userList?.length - 1]?.name;
        //   return callListCopy;
        // });

        endpoint = connect.Endpoint.byPhoneNumber(number);
      } else {
        // 直接使用手机号拨打
        notification.error({
          message: getIntl().formatMessage({
            id: 'new.worktable.phone.p',
          }),
        });
      }
    } else {
      let number = handleCountryCode(
        callList?.userList?.[callList?.userList?.length - 1]?.keyNumberArea +
          callList?.userList?.[callList?.userList?.length - 1]?.keyNumber,
      );
      endpoint = connect.Endpoint.byPhoneNumber(number);
    }
    console.log(
      endpoint,
      contact.getContactId(),
      contact.getInitialConnection(),
      contact.getConnections(),
      contact.getInitialContactId(),
      contact.getAgentConnection(),
      agent.getContacts(),
      '触发多人端点====',
    );
    agent.getContacts(connect.ContactType.VOICE)[0].addConnection(endpoint, {
      success: function(data) {
        setDrawerOpen(false);
        handleCallStatus(false, 'connect');
        handleCallStatus(false, 'keep');
        setCallingShow('multiple');
        setCurrentQCUser(null);
        addPhoneContactDetail({
          workOrderId: callList.ticketId, //工单id,在Connected中获取
          contactId: contact.getInitialContactId(),
          instanceId: callList.connectId,
          eventType: '7', //1、响铃 2、接听 3、呼出 4、ACW事件 5、挂断 6、暂停 7、转接
          // 初始ID
          initialContactId: contact.getInitialContactId(),
          // 下一个联络ID
          nextContactId: '',
          // 上一个联络ID
          previousContactId: '',
        });
        console.log(
          data,
          callList,
          contact.getContactId(),
          contact.getInitialConnection(),
          contact.getConnections(),
          contact.getInitialContactId(),
          contact.getAgentConnection(),
          '触发多人连接成功============',
        );
      },
      failure: function(err) {
        console.log(err, '触发多人连接失败============');
      },
    });
  };
  //选中快速连接中联系人列表
  const selectQuickConnect = it => {
    setCurrentQCUser(it);
  };
  //结束呼叫
  const destroyCall = async (value, tag) => {
    // if (callingShow === 'callOut') {
    let contact = new connect.Contact(callList.contactId);
    if (contact) {
      if (value === 'multiple') {
        var initialConnection = contact.getAgentConnection();
        if (initialConnection) {
          console.log(
            contact,
            initialConnection,
            '触发多人destroyCall==============',
          );
          initialConnection.destroy();
        }
      } else {
        console.log(contact, '触发destroyCall==============');
        var initialConnection = contact.getInitialConnection();
        if (initialConnection) {
          initialConnection.destroy();
          addPhoneContactDetail({
            workOrderId: callList.ticketId, //工单id,在Connected中获取
            contactId: callList.contactId,
            instanceId: callList.connectId,
            eventType: '5', //1、响铃 2、接听 3、呼出 4、ACW事件 5、挂断 6、暂停 7、转接
            endTime: moment()
              .utc()
              .add(8, 'hours')
              .format('YYYY-MM-DD HH:mm:ss'), // 挂断时间
            hangingType: '1', // 挂断类型 1、坐席结束  2、客户结束
          });
        }
        // }
      }
      const payload = {
        contactId: contact.getContactId(),
        ticketLanguage: contact.getAttributes().languageCode?.value,
      };
      syncCallRecord(payload);
    }
  };
  //拒绝呼入
  const rejectCall = () => {
    try {
      if (['callIn', 'videoCallin'].includes(callingShow)) {
        let contact = new connect.Contact(callList.contactId);
        if (contact) {
          contact.reject({
            success: function() {
              setAlertShow(false);
              console.log('触发拒绝呼入======');
              callingShowChange('initial');
              contact?.clear({
                success: function() {
                  console.log('触发清除呼入======');
                },
                failure: function(err) {
                  console.log('触发清除呼入clear==error======', err);
                },
              });
            },
            failure: function(err) {
              console.log('触发拒绝呼入error======执行clear', err);
              setAlertShow(true);
              setAlertText(
                getIntl().formatMessage({
                  id: 'im.chat.translation.retry.re',
                }),
              );
            },
          });
        }
      }
    } catch (error) {
      console.log(error, '触发拒绝呼入error======执行clear');
    }
  };
  //接受呼入
  const receiveCall = () => {
    if (['callIn', 'videoCallin'].includes(callingShow)) {
      let contact = new connect.Contact(callList.contactId);
      if (contact) {
        contact.accept({
          success: function() {
            setAlertShow(false);
            console.log('触发接受呼入======');
          },
          failure: function(err) {
            setAlertShow(true);
            setAlertText(
              getIntl().formatMessage({
                id: 'im.chat.translation.retry.re',
              }),
            );
            console.log('触发接受呼入error======', err);
          },
        });
      }
    }
  };
  //保持通话功能
  const onKeepCall = value => {
    let contact = new connect.Contact(callList.contactId);
    let initialConnection = null;
    handleCallStatus(true, 'keep');
    if (value !== 'multiple') {
      //会话中联系人列表
      let connectionList = contact.getConnections();
      handleCallList('userList', [
        connectionList[connectionList?.length - 1].connectionId,
        [['callingStatus', 'keep', true]],
      ]);
      initialConnection = contact.getInitialConnection();
    } else {
      initialConnection = contact.getAgentConnection();
    }
    if (contact && initialConnection) {
      console.log(initialConnection, '触发onKeepCall==============');
      initialConnection.hold();
      if (value !== 'multiple') {
        // 记录开始保持时间
        setKeepInterval(Math.floor(Date.now() / 1000));
      }
    }
  };
  //恢复通话功能
  const onNOKeepCall = value => {
    let contact = new connect.Contact(callList.contactId);
    let initialConnection = null;
    handleCallStatus(false, 'keep');

    if (value !== 'multiple') {
      //会话中联系人列表
      let connectionList = contact.getConnections();
      handleCallList('userList', [
        connectionList[connectionList?.length - 1].connectionId,
        [['callingStatus', 'keep', false]],
      ]);
      initialConnection = contact.getInitialConnection();
    } else {
      initialConnection = contact.getAgentConnection();
    }
    if (contact && initialConnection) {
      console.log(initialConnection, '触发onNOKeepCall==============');
      initialConnection.resume();
      if (value !== 'multiple') {
        // 计算保持时长并调用接口
        const endTime = Math.floor(Date.now() / 1000);
        const duration = endTime - keepInterval;
        addPhoneContactDetail({
          onHoldTime: duration,
          eventType: '6',
          workOrderId: callList.ticketId, //工单id,在Connected中获取
          contactId: callList.contactId,
          instanceId: callList.connectId,
        });
      }
    }
  };
  //静音
  const onSilenceCall = value => {
    handleCallStatus(true, 'sil');

    if (value !== 'multiple') {
      let contact = new connect.Contact(callList.contactId);
      //会话中联系人列表
      let connectionList = contact.getConnections();
      handleCallList('userList', [
        connectionList[connectionList?.length - 1].connectionId,
        [['callingStatus', 'sil', true]],
      ]);
    }

    let agent = new connect.Agent();
    if (agent) {
      console.log(agent, '触发onSilenceCall==============');
      agent.mute();
    }
  };
  //解除静音
  const onNOSilenceCall = value => {
    handleCallStatus(false, 'sil');

    if (value !== 'multiple') {
      let contact = new connect.Contact(callList.contactId);
      //会话中联系人列表
      let connectionList = contact.getConnections();
      handleCallList('userList', [
        connectionList[connectionList?.length - 1].connectionId,
        [['callingStatus', 'sil', false]],
      ]);
    }
    let agent = new connect.Agent();
    if (agent) {
      console.log(agent, '触发onNOSilenceCall==============');
      agent.unmute();
    }
  };
  //通话进入acw状态，关闭acw状态需要清空通话列
  const closeACW = async () => {
    callingShowChange('initial');
    let contact = new connect.Contact(callList.contactId);
    contact?.clear({
      success: function() {
        console.log('触发清除呼入======');
      },
      failure: function(err) {
        console.log('触发清除呼入error======', err);
      },
    });
  };
  /**
   * 多人对话保持通话功能
   */
  const setOneKeep = data => {
    let contact = new connect.Contact(callList.contactId);
    let initialConnection = contact?.getConnections();
    initialConnection?.forEach(item => {
      if (item.connectionId === data.connectionId) {
        console.log(item, '触发setOneKeep==============');
        handleCallList('userList', [
          data.connectionId,
          [['callingStatus', 'keep', true]],
        ]);
        item.hold();
      }
    });
  };
  /**
   * 多人对话结束保持通话功能
   */
  const setNoOneKeep = data => {
    console.log(data);
    let contact = new connect.Contact(callList.contactId);
    let initialConnection = contact?.getConnections();
    initialConnection?.forEach(item => {
      if (item.connectionId === data.connectionId) {
        console.log(item, '触发setNoOneKeep==============');
        handleCallList('userList', [
          data.connectionId,
          [['callingStatus', 'keep', false]],
        ]);
        item.resume();
      }
    });
  };
  /**
   * 多人对话静音功能
   */
  const setOneSil = data => {
    let contact = new connect.Contact(callList.contactId);
    let initialConnection = contact?.getConnections();
    initialConnection?.forEach(item => {
      if (item.connectionId === data.connectionId) {
        item.muteParticipant({
          success: function() {
            handleCallList('userList', [
              data.connectionId,
              [['callingStatus', 'sil', true]],
            ]);
            console.log(item, '触发setOneSil==============');
          },
          failure: function(err) {
            console.log(err, '触发setOneSil失败==============');
          },
        });
      }
    });
  };
  /**
   * 多人对话取消静音功能
   */
  const setNoOneSil = data => {
    let contact = new connect.Contact(callList.contactId);
    let initialConnection = contact?.getConnections();
    initialConnection?.forEach(item => {
      if (item.connectionId === data.connectionId) {
        item.unmuteParticipant({
          success: function() {
            handleCallList('userList', [
              data.connectionId,
              [['callingStatus', 'sil', false]],
            ]);
            console.log(item, '触发setNoOneSil==============');
          },
          failure: function(err) {
            console.log(err, '触发setNoOneSil失败==============');
          },
        });
      }
    });
  };
  /**
   * 多人对话挂掉某一个成员功能
   */
  const setOneEnded = (data, i) => {
    if (data.connectionId) {
      let contact = new connect.Contact(callList.contactId);
      let initialConnection = contact?.getConnections();
      initialConnection?.forEach(item => {
        console.log(item, data, '触发注销中=========');
        if (item.connectionId === data.connectionId) {
          item.destroy();
        }
      });
    } else {
      setCallList(pre => {
        const callListCopy = { ...pre };
        callListCopy.userList.splice(i, 1);
        return callListCopy;
      });
    }
  };
  //计时器逻辑
  useEffect(() => {
    let interval = null;
    // 呼出呼入界面计时器
    if (['callOut', 'callIn', 'videoCallin'].includes(callingShow)) {
      interval = setInterval(() => {
        handleCallList('userList', ['', [['timeKeeping', '']]]);
      }, 1000);
    } else if (
      ['calling', 'multiple', 'videoCalling', 'videoACW', 'callACW'].includes(
        callingShow,
      )
    ) {
      let contact = new connect.Contact(callList.contactId);
      //会话中联系人列表
      let connectionList = contact.getConnections();
      interval = setInterval(() => {
        connectionList.forEach((item, index) => {
          if (index) {
            handleCallList('userList', [
              item.connectionId,
              [['timeKeeping', '']],
            ]);
          }
        });
      }, 1000);
    } else if (
      ![
        'callOut',
        'callIn',
        'calling',
        'videoCalling',
        'videoCallin',
        'callACW',
        'videoACW',
      ].includes(callingShow)
    ) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [
    callingShow,
    callList.userList[callList.userList.length - 1]?.connectionId,
  ]);
  //监控状态
  useEffect(() => {
    //acw状态重新计时
    if (callingShow === 'videoACW' || callingShow === 'callACW') {
      let contact = new connect.Contact(callList.contactId);
      //会话中联系人列表
      let connectionList = contact.getConnections();
      connectionList.forEach((item, index) => {
        handleCallList('userList', [item.connectionId, [['timeKeeping', 0]]]);
      });
      setTicketInfo({
        ...ticketInfo,
        phoneAcwStatus: false,
      });
      getPhoneCurrentInfo({
        ...ticketInfo,
        phoneAcwStatus: false,
      });
      setCallingStatus([]);
    }
    //回到初始页面需要清空一些数据
    if (callingShow === 'initial') {
      // setTimeKeeping(0);
      setCallList({
        connectId: selectedConnect.connectId,
        ticketId: '',
        contactId: '',
        userList: [
          {
            connectionId: '',
            keyNumberArea: regionTimezones?.[0]?.telephonePrefix,
            keyNumber: '',
            timeKeeping: 0,
            role: '',
            callingStatus: [],
            status: '',
            name: '',
          },
        ],
      });
      setTicketInfo(null);
      getPhoneCurrentInfo({});
    }
    if (isValidJson(localStorage.getItem('callLog')) && callLogsExist) {
      return;
    }
    //接通电话需要清空计时器
    if (
      (callingShow === 'calling' || callingShow === 'videoCalling') &&
      !callingStatus.includes('connect')
    ) {
      let contact = new connect.Contact(callList.contactId);
      //会话中联系人列表
      let connectionList = contact.getConnections();
      handleCallList('userList', [
        connectionList[connectionList?.length - 1].connectionId,
        [['timeKeeping', 0]],
      ]);
      setCallingStatus([]);
    }
  }, [callingShow]);
  //自动创建工单
  const autoCreateWork = (payload, contactDetail) => {
    dispatch({
      type: 'worktable/autoCreateWork',
      payload: payload,
      callback: response => {
        if (response.code === 200) {
          handleCallList('ticketId', response.data);
          sessionStorage.setItem('autoCreateWorkID', response.data);
          //这里需要处理外呼接听前未补全的数据，eventType为8
          addPhoneContactDetail({
            workOrderId: response.data, //工单id,这是呼出补全的数据
            ...contactDetail,
          });
          dispatch({
            type: 'workOrderCenter/queryWorkOrderDetail',
            payload: response.data,
            callback: response => {
              if (response.code == 200) {
                setTicketInfo({
                  ...response.data,
                  phoneNumber: payload.customerContactInfo,
                  voiceName: payload.chatUserName,
                  phoneAcwStatus: true,
                });
                getPhoneCurrentInfo({
                  ...response.data,
                  phoneNumber: payload.customerContactInfo,
                  voiceName: payload.chatUserName,
                  phoneAcwStatus: true,
                });
              } else {
                notification.error({
                  message: response.msg,
                });
              }
            },
          });
        } else {
          notification.error({
            message: getIntl().formatMessage({
              id: 'new.worktable.phone.create.fail',
            }),
          });
        }
      },
    });
  };
  //聊天窗信息外呼电话
  const componentCallOut = async value => {
    let tempValue = value?.split('+')[value?.split('+')?.length - 1];
    let phoneNumber = '+' + tempValue;
    let phoneNumberInfo = parsePhoneNumberFromString(phoneNumber);
    //判断是否为有效电话
    if (phoneNumberInfo?.country && phoneNumberInfo?.number) {
      if (callingShow !== 'initial') {
        notification.error({
          message: getIntl().formatMessage({
            id: 'new.worktable.phone.calling',
          }),
        });
      } else {
        try {
          //处理从聊天窗口外呼，存入电话号码
          const phoneNumber = parsePhoneNumber(phoneNumberInfo.number); // 输入号码
          handleCallList('userList', [
            '',
            [
              [
                'keyNumber',
                phoneNumber.nationalNumber[0] +
                  '******' +
                  phoneNumber.nationalNumber[
                    phoneNumber.nationalNumber.length - 1
                  ],
              ],
              ['keyNumberArea', '+' + String(phoneNumber.countryCallingCode)],
            ],
          ]);
          let endpoint = connect.Endpoint.byPhoneNumber(phoneNumberInfo.number);
          console.log(callList, endpoint, '外呼外呼电话====');
          let agent = new connect.Agent();
          let queueArns = agent.getAllQueueARNs();
          if (queueArns.length >= 1) {
            agent.connect(endpoint, {
              queueARN: queueArns[0],
              success: function(res) {},
              failure: function(err) {
                console.log('outbound call connection failed');
                console.log(err);

                let error = isValidJson(err) ? JSON.parse(err) : err;
                notification.error({
                  message: error?.message,
                });
              },
            });
          }
          setChatPhone(phoneNumberInfo.number);
        } catch (error) {
          notification.error({
            message: 'The agent is not yet initialized!',
          });
        }
      }
    } else {
      notification.error({
        message: getIntl().formatMessage({
          id: 'new.worktable.phone.calling.fail',
        }),
      });
    }
  };

  useImperativeHandle(ref, () => ({
    componentCallOut,
  }));
  const onCloseAlert = () => {
    setAlertShow(false);
  };
  /**
   * 播放来电铃声
   */
  const playAudio = () => {
    // 重置音频
    downKeyAudio.currentTime = 0;
    downKeyAudio.play();
  };
  /**
   * 调用记录通话中指标的接口
   */
  const addPhoneContactDetail = payload => {
    console.log(payload, callList, '触发addPhoneContactDetail==============');
    if (!payload.workOrderId) {
      payload.workOrderId = callList.ticketId;
    }
    dispatch({
      type: 'worktable/addPhoneContactDetail',
      payload: payload,
    });
  };
  return ccpError ? (
    <div className={styles.contentBoxPhoneError}>
      <Alert
        description={alertText}
        type="error"
        closable
        onClose={() => onCloseAlert()}
        style={{
          display: alertShow ? '' : 'none',
          position: 'absolute',
          zIndex: 100,
          width: '80%',
          left: '10%',
          top: '5%',
          borderRadius: 4,
          border: '1px solid #F22417',
          background:
            'linear-gradient(0deg, rgba(242, 36, 23, 0.10) 0%, rgba(242, 36, 23, 0.10) 100%), rgba(255, 255, 255, 0.80)',
          boxShadow: '0px 4px 10px 0px rgba(0, 0, 0, 0.15)',
          backdropFilter: 'blur(2px)',
        }}
      />
      <div
        style={{
          margin: 'auto',
          display: 'flex',
          flexDirection: 'column',
          gap: 10,
        }}
      >
        <img src={ccpErrorImg} style={{ width: '100%', height: 'auto' }}></img>
        <p style={{ color: '#999', textAlign: 'center', fontSize: 12 }}>
          <FormattedMessage
            id="new.worktable.phone.refresh"
            values={{
              refresh: (
                <span
                  style={{ color: '#3463FC', cursor: 'pointer' }}
                  onClick={() => {
                    location.reload();
                  }}
                >
                  <FormattedMessage id="new.worktable.phone.refresh.Retry" />
                </span>
              ),
            }}
          />
        </p>
      </div>
    </div>
  ) : (
    <>
      <div
        style={{
          background: ['initial', 'callOut', 'callIn', 'videoCallin'].includes(
            callingShow,
          )
            ? 'rgba(255, 255, 255, 0.6)'
            : ['callACW', 'videoACW'].includes(callingShow)
            ? 'linear-gradient(180deg, rgba(255, 255, 255, 0.10) 0%, rgba(33, 38, 43, 0.10) 100%), #FFF'
            : ['calling', 'videoCalling'].includes(callingShow) &&
              !callingStatus.includes('sil') &&
              !callingStatus.includes('keep')
            ? 'linear-gradient(180deg, #FFF 0%, rgba(255, 255, 255, 0.00) 5%), linear-gradient(180deg, rgba(235, 247, 228, 0.10) 0%, rgba(19, 200, 37, 0.10) 100%)'
            : ['calling', 'videoCalling'].includes(callingShow) &&
              (callingStatus.includes('sil') || callingStatus.includes('keep'))
            ? 'linear-gradient(180deg, #FFF 0%, rgba(255, 255, 255, 0.00) 5%), linear-gradient(180deg, rgba(250, 233, 225, 0.10) 0%, rgba(255, 211, 1, 0.10) 100%)'
            : callingShow === 'multiple'
            ? 'linear-gradient(180deg, rgba(255, 255, 255, 0.10) 0%, rgba(52, 99, 252, 0.10) 74.5%), #FFF'
            : '#fff',
        }}
        className={styles.contentBoxPhone}
      >
        {/**********************************************************************错误提示**********************************************************************/}
        <Alert
          description={alertText}
          type="error"
          closable
          onClose={() => onCloseAlert()}
          style={{
            display: alertShow ? '' : 'none',
            position: 'absolute',
            zIndex: 100,
            width: '80%',
            left: '10%',
            top: '5%',
            borderRadius: 4,
            border: '1px solid #F22417',
            background:
              'linear-gradient(0deg, rgba(242, 36, 23, 0.10) 0%, rgba(242, 36, 23, 0.10) 100%), rgba(255, 255, 255, 0.80)',
            boxShadow: '0px 4px 10px 0px rgba(0, 0, 0, 0.15)',
            backdropFilter: 'blur(2px)',
          }}
        />
        <Spin spinning={ccpLoading}>
          {/*********************************************************************** 初始状态 ***********************************************************************/}
          <div
            className={styles.contentBoxInitial}
            style={{ display: callingShow === 'initial' ? '' : 'none' }}
          >
            <div className={styles.contentBoxPhoneTitle}>
              <FormattedMessage
                id="new.worktable.phone.title"
                defaultMessage="数字键盘"
              />
            </div>
            <div className={styles.contentBoxPhoneConnect}>
              <p>
                <FormattedMessage
                  id="new.worktable.phone.connect"
                  defaultMessage="选择 Connect 实例"
                />
              </p>
              <Select
                placeholder={getIntl().formatMessage({
                  id: 'new.worktable.phone.connect.p',
                })}
                options={connectList}
                showSearch
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
                onChange={(value, item) => onChangeConncet(value, item)}
                value={callList.connectId}
              />
            </div>
            <div className={styles.contentBoxPhoneNumber}>
              <Space direction="vertical" id={'spaceBox'}>
                <p>
                  <FormattedMessage
                    id="new.worktable.phone.number"
                    defaultMessage="请输入手机号码"
                  />
                </p>
                <Space.Compact>
                  <Select
                    value={callList?.userList[0]?.keyNumberArea}
                    getPopupContainer={() =>
                      document.getElementById('spaceBox')
                    }
                    showSearch
                    onChange={e =>
                      handleCallList('userList', ['', [['keyNumberArea', e]]])
                    }
                  >
                    {regionTimezones?.map(item => {
                      const Icon = Flags[item.svgIcon];
                      return (
                        <Select.Option
                          value={item.telephonePrefix}
                          className={styles.regionSelect}
                        >
                          <Space>
                            <span>{Icon ? <Icon /> : null}</span>
                            <span
                              style={{
                                marginRight: Icon ? 20 : 40,
                                fontSize: 12,
                              }}
                            >
                              {item.telephonePrefix}
                            </span>
                            {item.countryEn}
                          </Space>
                        </Select.Option>
                      );
                    })}
                  </Select>
                  <div
                    style={{
                      border: '0.1px #999 solid',
                      height: 16,
                      opacity: '0.4',
                      background: '#999',
                      marginTop: 12,
                    }}
                  ></div>
                  <Input
                    value={callList?.userList[0]?.keyNumber}
                    onChange={e => {
                      playAudio();
                      handleKeyNumber(e.target.value);
                    }}
                    onPressEnter={e => onPressEnter(e)}
                    placeholder={getIntl().formatMessage({
                      id: 'new.worktable.phone.number',
                    })}
                  />
                  <span
                    style={{
                      position: 'absolute',
                      right: 12,
                      top: 40,
                      cursor: 'pointer',
                      zIndex: 999,
                    }}
                    onClick={() =>
                      handleCallList('userList', ['', [['keyNumber', '']]])
                    }
                  >
                    {InputCloseIcon()}
                  </span>
                </Space.Compact>
              </Space>
            </div>
            <div className={styles.contentBoxPhonekeyboard}>
              {['1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '0', '#'].map(
                item => {
                  return (
                    <div
                      className={styles.item}
                      onClick={() => passKey(0, item)}
                    >
                      {item}
                    </div>
                  );
                },
              )}
            </div>
            <div className={styles.contentBoxPhoneButton}>
              <Button
                onClick={() => {
                  outBoundCall(
                    callList.userList[0].keyNumberArea +
                      callList.userList[0].keyNumber,
                  );
                }}
              >
                {CallIcon()}
                <FormattedMessage
                  id="new.worktable.phone.call"
                  defaultMessage="呼叫"
                />
              </Button>
            </div>
          </div>
          {/******************************************************** 通话状态下的样式 videoRTCCapabilities===true代表视频电话*******************************************/}
          <div
            className={styles.contentBoxCall}
            style={{
              display: [
                'callOut',
                'callACW',
                'callIn',
                'calling',
                'videoCalling',
                'videoCallin',
                'videoACW',
              ].includes(callingShow)
                ? ''
                : 'none',
            }}
            id="callBox"
          >
            {/*********************************************************************** 电话 ***********************************************************************/}
            {['videoCallin', 'videoCalling'].includes(callingShow) &&
            videoRTCCapabilities ? (
              <div>
                <img
                  src={agent}
                  style={{
                    width: 100,
                    height: 100,
                    marginTop: 80,
                    marginBottom: 20,
                  }}
                />
                <div
                  style={{ textAlign: 'center', fontSize: 16, fontWeight: 700 }}
                >
                  {callList.userList[0].name}
                </div>
              </div>
            ) : ['videoACW'].includes(callingShow) && videoRTCCapabilities ? (
              <div className={styles.numberCall}>
                {callList.userList[0].name}
              </div>
            ) : (
              <div className={styles.numberCall}>
                {callList.userList[0]?.keyNumberArea ??
                  sessionStorage.getItem('AfterKeyNumberArea')}
                &nbsp;&nbsp;
                {formatKeyNumber(callList.userList[0]?.keyNumber)}
              </div>
            )}
            {/* **********************************************************************连接状态 ***********************************************************************/}
            <div
              className={styles.connectionCall}
              style={{
                marginBottom: ['videoCallin', 'videoCalling'].includes(
                  callingShow,
                )
                  ? 3
                  : '',
              }}
            >
              {callingShow === 'callACW' || callingShow === 'videoACW' ? (
                <span style={{ color: '#666' }}>
                  <FormattedMessage
                    id="new.worktable.phone.callACW"
                    defaultMessage="通话结束后的工作"
                  />
                </span>
              ) : (callingShow === 'calling' ||
                  callingShow === 'videoCalling') &&
                callingStatus.length == 0 ? (
                <span
                  style={{
                    color: callingShow === 'videoCalling' ? '#333' : '#13C825',
                    fontWeight: 700,
                    fontSize: callingShow === 'videoCalling' ? 12 : '',
                  }}
                >
                  <FormattedMessage
                    id="new.worktable.phone.calling"
                    defaultMessage="正在通话"
                  />
                </span>
              ) : (callingShow === 'calling' ||
                  callingShow === 'videoCalling') &&
                callingStatus.includes('keep') ? (
                <span
                  style={{
                    color: '#FFD301',
                    fontWeight: 700,
                    fontSize: callingShow === 'videoCalling' ? 12 : '',
                  }}
                >
                  <FormattedMessage
                    id="new.worktable.phone.callKeep"
                    defaultMessage="通话保持中"
                  />
                </span>
              ) : (callingShow === 'calling' ||
                  callingShow === 'videoCalling') &&
                callingStatus.includes('sil') ? (
                <span
                  style={{
                    color: '#FFD301',
                    fontWeight: 700,
                    fontSize: callingShow === 'videoCalling' ? 12 : '',
                  }}
                >
                  <FormattedMessage
                    id="new.worktable.phone.callSil"
                    defaultMessage="静音中"
                  />
                </span>
              ) : callingShow === 'callIn' ? (
                <FormattedMessage
                  id="new.worktable.phone.callin"
                  defaultMessage="新的来电"
                />
              ) : (
                <span
                  style={{ fontSize: callingShow === 'videoCallin' ? 12 : '' }}
                >
                  <FormattedMessage
                    id="new.worktable.phone.callout"
                    defaultMessage="正在连接"
                  />
                </span>
              )}
            </div>
            {/*********************************************************************** 计时器 ***********************************************************************/}
            <div
              className={styles.timeCall}
              style={{
                fontSize: ['videoCallin', 'videoCalling'].includes(callingShow)
                  ? 12
                  : '',
              }}
            >
              {callingShow !== 'callACW' &&
              callingShow !== 'videoCalling' &&
              callingShow !== 'videoCallin' &&
              callingShow !== 'videoACW'
                ? CallTimeIcon()
                : ''}
              {formatTime(callList?.userList[0]?.timeKeeping)}
            </div>
            {/*********************************************************************** 偏好语言 ***********************************************************************/}

            {['callIn', 'videoCallin'].includes(callingShow) &&
              contactLanguageLabel && (
                <div
                  className={styles.connectionLangCall}
                  style={{
                    marginTop: callingShow === 'videoCallin' ? 10 : 20,
                    fontSize: callingShow === 'videoCallin' ? 12 : 14,
                  }}
                >
                  <FormattedMessage
                    id="AIGC.chatBox.language.preference"
                    defaultMessage="偏好语言"
                  />
                  &nbsp;:&nbsp;&nbsp;<span>{contactLanguageLabel}</span>
                </div>
              )}
            {/*********************************************************************** 通话中的功能按钮 ***********************************************************************/}
            {callingShow === 'calling' && !videoRTCCapabilities ? (
              <div className={styles.keyBtnCall}>
                {callingStatus.includes('keep') ? (
                  <Button
                    onClick={() => onNOKeepCall()}
                    style={{ background: '#AD30E5' }}
                  >
                    {CallPlayIcon(16, 16)}
                    <span style={{ color: '#fff' }}>
                      <FormattedMessage
                        id="new.worktable.phone.callBtn.1.target"
                        defaultMessage="恢复通话"
                      />
                    </span>
                  </Button>
                ) : (
                  <Button onClick={() => onKeepCall()}>
                    {CallKeepIcon(16, 16)}
                    <FormattedMessage
                      id="new.worktable.phone.callBtn.1"
                      defaultMessage="保持通话"
                    />
                  </Button>
                )}

                <Button
                  onClick={() => {
                    setDrawerOpen(true);
                    // pushUserList('multiple');
                  }}
                >
                  {CallKeyBoardIcon()}
                  <FormattedMessage
                    id="new.worktable.phone.callBtn.2"
                    defaultMessage="数字键盘"
                  />
                </Button>
                {callingStatus.includes('sil') ? (
                  <Button
                    onClick={() => onNOSilenceCall()}
                    style={{ background: '#AD30E5' }}
                  >
                    {CallSlinoIcon(16, 16)}
                    <span style={{ color: '#fff' }}>
                      <FormattedMessage
                        id="new.worktable.phone.callBtn.3"
                        defaultMessage="静音"
                      />
                    </span>
                  </Button>
                ) : (
                  <Button onClick={() => onSilenceCall()}>
                    {CallSliIcon(16, 16)}
                    <FormattedMessage
                      id="new.worktable.phone.callBtn.3"
                      defaultMessage="静音"
                    />
                  </Button>
                )}
                <Button
                  onClick={() => {
                    handleCallStatus(true, 'connect');
                    pushUserList('multiple');
                    listQueueQuickConnects();
                  }}
                >
                  {CallConnectFastIcon()}
                  <FormattedMessage
                    id="new.worktable.phone.callBtn.4"
                    defaultMessage="快速连接"
                  />
                </Button>
              </div>
            ) : (
              ''
            )}
            {/*********************************************************************** 通话前的按钮 拒绝 接受 ***********************************************************************/}
            {['callOut', 'calling'].includes(callingShow) ? (
              <div className={styles.endCall}>
                <Button onClick={() => destroyCall()}>
                  {CallEndTimeIcon()}
                  <FormattedMessage
                    id="new.worktable.phone.callEnd"
                    defaultMessage="结束通话"
                  />
                </Button>
              </div>
            ) : ['callIn', 'videoCallin'].includes(callingShow) ? (
              <div className={styles.endCallinOut}>
                <div className={styles.endCallin}>
                  <Button onClick={() => receiveCall()}>
                    {CallIcon()}
                    <FormattedMessage
                      id="new.worktable.phone.callReceive"
                      defaultMessage="接听电话"
                    />
                  </Button>
                </div>
                <div className={styles.endCallout}>
                  <Button onClick={() => rejectCall()}>
                    {CallEndTimeIcon()}
                    <FormattedMessage
                      id="new.worktable.phone.callReject"
                      defaultMessage="拒绝电话"
                    />
                  </Button>
                </div>
              </div>
            ) : ['videoCalling'].includes(callingShow) &&
              videoRTCCapabilities ? (
              <div className={styles.contentBoxPhoneButton}>
                {callingStatus.includes('keep') ? (
                  <div
                    className={styles.contentBoxPhoneButtonItemStop}
                    style={{
                      backgroundColor: '#AD30E5',
                    }}
                    onClick={() => onNOKeepCall()}
                  >
                    {CallPlayIcon(25, 25)}
                  </div>
                ) : (
                  <div
                    className={styles.contentBoxPhoneButtonItemStop}
                    style={{
                      backgroundColor: '#fff',
                    }}
                    onClick={() => onKeepCall()}
                  >
                    {CallKeepIcon(25, 25)}
                  </div>
                )}
                {callingStatus.includes('sil') ? (
                  <div
                    className={styles.contentBoxPhoneButtonItemSli}
                    style={{
                      backgroundColor: '#AD30E5',
                    }}
                    onClick={() => onNOSilenceCall()}
                  >
                    {CallSlinoIcon(25, 25)}
                  </div>
                ) : (
                  <div
                    className={styles.contentBoxPhoneButtonItemSli}
                    style={{
                      backgroundColor: '#fff',
                    }}
                    onClick={() => onSilenceCall()}
                  >
                    {CallSliIcon(25, 25)}
                  </div>
                )}
                <div
                  className={styles.contentBoxPhoneButtonItemYellow}
                  onClick={() => {
                    handleCallStatus(true, 'connect');
                    pushUserList('multiple');
                    listQueueQuickConnects();
                  }}
                >
                  {QuickConnectBtn()}
                </div>
                <div
                  className={styles.contentBoxPhoneButtonItemRed}
                  onClick={() => destroyCall()}
                >
                  {MultipleEndBtn(25, 25)}
                </div>
              </div>
            ) : (
              <div className={styles.endCallACW}>
                <div>
                  <FormattedMessage
                    id="im.chat.acw.open"
                    values={{
                      acw: (
                        <Tooltip
                          color="#92a4ec"
                          placement="top"
                          title={getIntl().formatMessage({
                            id: 'im.chat.acw.prompt',
                          })}
                        >
                          <span style={{ color: '#3463FC' }}>ACW</span>
                        </Tooltip>
                      ),
                    }}
                  />
                </div>
                <Button onClick={() => closeACW()}>
                  {CallCloseIcon()}
                  <FormattedMessage
                    id="new.worktable.phone.callClose"
                    defaultMessage="关闭通话"
                  />
                </Button>
              </div>
            )}
            {/*********************************************************************** 数字键盘抽屉 **********************************************************************/}
            <Drawer
              title={getIntl().formatMessage({
                id: 'new.worktable.phone.title',
              })}
              getContainer={() => document.getElementById('callBox')}
              placement={'bottom'}
              width={'100%'}
              height={'50%'}
              open={drawerOpen}
              closeIcon={DrawerCloseIcon()}
              onClose={() => {
                setDrawerOpen(false);
                setKeyboardValue('');
                // deleteUserList();
              }}
            >
              <div className={styles.contentBoxPhoneNumberDrawer}>
                <Space direction="vertical" id={'spaceBox1'}>
                  <Space.Compact>
                    {/* <Select
                      value={callList?.userList[1]?.keyNumberArea}
                      getPopupContainer={() =>
                        document.getElementById('spaceBox1')
                      }
                      showSearch
                      onChange={e =>
                        handleCallList('userList', ['', [['keyNumberArea', e]]])
                      }
                    >
                      {regionTimezones?.map(item => {
                        const Icon = Flags[item.svgIcon];
                        return (
                          <Select.Option
                            key={item.telephonePrefix}
                            value={item.telephonePrefix}
                            className={styles.regionSelect}
                          >
                            <Space>
                              {Icon ? <Icon /> : null}
                              <span style={{ marginRight: 20, fontSize: 12 }}>
                                {item.telephonePrefix}
                              </span>
                              {item.countryEn}
                            </Space>
                          </Select.Option>
                        );
                      })}
                    </Select>
                   <div
                      style={{
                        border: '0.1px #999 solid',
                        height: 16,
                        opacity: '0.4',
                        background: '#999',
                        marginTop: 12,
                      }}
                    ></div> */}
                    <Input
                      value={keyboardValue}
                      onChange={e => {
                        playAudio();
                        setKeyboardValue(e.target.value);
                      }}
                      // onPressEnter={e => onPressEnter(e)}
                      placeholder={getIntl().formatMessage({
                        id: 'channel.please.input',
                      })}
                    />
                    <span
                      style={{
                        position: 'absolute',
                        right: 12,
                        top: 12,
                        cursor: 'pointer',
                        zIndex: 999,
                      }}
                      onClick={() => setKeyboardValue('')}
                    >
                      {InputCloseIcon()}
                    </span>
                  </Space.Compact>
                </Space>
              </div>
              <div className={styles.contentBoxPhonekeyboard}>
                {[
                  '1',
                  '2',
                  '3',
                  '4',
                  '5',
                  '6',
                  '7',
                  '8',
                  '9',
                  '*',
                  '0',
                  '#',
                ].map(item => {
                  return (
                    <div
                      className={styles.item}
                      onClick={() => passKey(1, item)}
                    >
                      {item}
                    </div>
                  );
                })}
              </div>
              {/* <div className={styles.contentBoxPhoneButtonDrawer}>
                <Button onClick={() => multiConnectCall()}>
                  {CallIcon()}
                  <FormattedMessage
                    id="new.worktable.phone.call"
                    defaultMessage="呼叫"
                  />
                </Button>
              </div> */}
            </Drawer>
          </div>
          {/* ***********************************************************************快速连接 ***********************************************************************/}
          <div
            className={styles.contentBoxQuick}
            style={{
              display: callingStatus.includes('connect') ? '' : 'none',
            }}
          >
            <Spin spinning={quickConnectLoading}>
              <div className={styles.contentBoxPhoneTitle}>
                <FormattedMessage
                  id="new.worktable.phone.call.quickConnect"
                  defaultMessage="快速连接"
                />
              </div>

              <div className={styles.contentBoxPhoneNumber}>
                <Space direction="vertical" id={'spaceBox2'}>
                  <p>
                    <FormattedMessage
                      id="new.worktable.phone.number"
                      defaultMessage="请输入手机号码"
                    />
                  </p>
                  <Space.Compact>
                    <Select
                      value={
                        callList?.userList?.[callList?.userList?.length - 1]
                          ?.keyNumberArea
                      }
                      getPopupContainer={() =>
                        document.getElementById('spaceBox2')
                      }
                      showSearch
                      onChange={e =>
                        handleCallList('userList', ['', [['keyNumberArea', e]]])
                      }
                    >
                      {regionTimezones?.map(item => {
                        const Icon = Flags[item.svgIcon];
                        return (
                          <Select.Option
                            value={item.telephonePrefix}
                            className={styles.regionSelect}
                          >
                            <Space>
                              {Icon ? <Icon /> : null}
                              <span style={{ marginRight: 20, fontSize: 12 }}>
                                {item.telephonePrefix}
                              </span>
                              {item.countryEn}
                            </Space>
                          </Select.Option>
                        );
                      })}
                    </Select>
                    <div
                      style={{
                        border: '0.1px #999 solid',
                        height: 16,
                        opacity: '0.4',
                        background: '#999',
                        marginTop: 12,
                      }}
                    ></div>
                    <Input
                      value={
                        callList?.userList?.[callList?.userList?.length - 1]
                          ?.name
                      }
                      onChange={e => handleSearchKeyNumber(e.target.value)}
                      placeholder={getIntl().formatMessage({
                        id: 'new.worktable.phone.number',
                      })}
                    />
                  </Space.Compact>
                </Space>
              </div>
              <div className={styles.contentBoxPhoneConnect}>
                <p>
                  <FormattedMessage
                    id="new.worktable.phone.call.quickConnect.agent.p"
                    defaultMessage="请选择要转接的座席或队列"
                  />
                </p>

                <div className={styles.contentBoxPhoneUserList}>
                  {filteredData?.map(item => {
                    return (
                      <div
                        className={styles.contentBoxPhoneUserListItem}
                        onClick={() => selectQuickConnect(item)}
                        style={{
                          backgroundColor:
                            currentQCUser?.endpointId === item.endpointId
                              ? 'rgba(173, 48, 229, 1)'
                              : '',
                          color:
                            currentQCUser?.endpointId === item.endpointId
                              ? '#fff'
                              : '',
                        }}
                      >
                        <div className={styles.contentBoxPhoneUserListFont}>
                          {item.name}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              <div className={styles.contentBoxPhoneButton}>
                <Button onClick={() => multiConnectCall('qucikConnect')}>
                  {CallIcon()}
                  <FormattedMessage
                    id="new.worktable.phone.call"
                    defaultMessage="呼叫"
                  />
                </Button>
                <Button
                  onClick={() => {
                    handleCallStatus(false, 'connect');
                    deleteUserList();
                    setCurrentQCUser(null);
                  }}
                >
                  {CallCloseIcon()}
                  <FormattedMessage
                    id="AIGC.Drawer.two.form.cancel"
                    defaultMessage="取消"
                  />
                </Button>
              </div>
            </Spin>
          </div>
          {/*********************************************************************** 多方通话 ***********************************************************************/}
          <div
            className={styles.contentBoxMultiple}
            style={{
              display: callingShow === 'multiple' ? '' : 'none',
            }}
          >
            <div className={styles.contentBoxMultipleTop}>
              <div className={styles.contentBoxPhoneTitle}>
                <FormattedMessage
                  id="new.worktable.phone.call.multiple.people"
                  defaultMessage="多方通话"
                />
              </div>
              <div className={styles.contentBoxPhoneList}>
                <Space direction="vertical">
                  {callList.userList?.map((item, index) => {
                    return (
                      <div
                        className={styles.contentBoxPhoneItem}
                        style={{
                          background: item.status?.includes('multiple')
                            ? '#AD30E5'
                            : '',
                        }}
                      >
                        <div className={styles.contentBoxPhoneItemContent}>
                          <span>
                            {item.keyNumber ? (
                              <span
                                style={{
                                  color: item.status?.includes('multiple')
                                    ? '#fff'
                                    : '#333',
                                  fontFamily: 'Poppins, sans-serif',
                                  fontSize: 12,
                                  fontWeight: 700,
                                }}
                              >
                                {item.keyNumberArea ??
                                  sessionStorage.getItem('AfterKeyNumberArea')}
                                &nbsp; &nbsp;
                                {formatKeyNumber(item.keyNumber)}
                              </span>
                            ) : (
                              <span
                                style={{
                                  color: item.status?.includes('multiple')
                                    ? '#fff'
                                    : '#333',
                                  fontFamily: 'Poppins, sans-serif',
                                  fontSize: 12,
                                  fontWeight: 700,
                                }}
                              >
                                {item.name}
                              </span>
                            )}
                            {item.role}
                          </span>
                          <span
                            style={{
                              color: item.status?.includes('multiple')
                                ? '#fff'
                                : '#666',
                              fontFamily: 'Poppins, sans-serif',
                              fontSize: 12,
                              fontWeight: 400,
                              display: 'flex',
                              alignItems: 'center',
                            }}
                          >
                            {item.status?.includes('multiple')
                              ? CallTimeSmallBaiIcon()
                              : CallTimeSmallIcon()}
                            <span style={{ marginLeft: 3 }}>
                              {formatTime(item.timeKeeping)}
                            </span>
                          </span>
                        </div>
                        <div className={styles.contentBoxPhoneItemStatus}>
                          <div
                            style={{
                              display: 'inline-flex',
                              gap: 5,
                              alignItems: 'center',
                              justifyContent: 'flex-end',
                            }}
                          >
                            {item.status?.includes(
                              'multiple',
                            ) ? null : item.callingStatus?.includes('keep') ? (
                              <div
                                className={styles.contentBoxPhoneButtonItemStop}
                                style={{
                                  backgroundColor: '#AD30E5',
                                }}
                                onClick={() => setNoOneKeep(item)}
                              >
                                {CallPlayIcon(8, 8)}
                              </div>
                            ) : (
                              <div style={{ display: 'flex' }}>
                                {item.callingStatus?.includes('sil') ? (
                                  <div
                                    className={
                                      styles.contentBoxPhoneButtonItemSli
                                    }
                                    style={{
                                      backgroundColor: '#AD30E5',
                                    }}
                                    onClick={() => setNoOneSil(item)}
                                  >
                                    {CallSlinoIcon(8, 8)}
                                  </div>
                                ) : (
                                  <div
                                    className={
                                      styles.contentBoxPhoneButtonItemSli
                                    }
                                    style={{
                                      backgroundColor: '#fff',
                                    }}
                                    onClick={() => setOneSil(item)}
                                  >
                                    {CallSliIcon(8, 8)}
                                  </div>
                                )}
                                {!item.callingStatus?.includes('keep') ? (
                                  <div
                                    className={
                                      styles.contentBoxPhoneButtonItemStop
                                    }
                                    style={{
                                      backgroundColor: '#fff',
                                      marginLeft: 5,
                                    }}
                                    onClick={() => setOneKeep(item)}
                                  >
                                    {CallKeepIcon(8, 8)}
                                  </div>
                                ) : (
                                  ''
                                )}
                              </div>
                            )}
                            <div
                              className={styles.contentBoxPhoneButtonItemRed}
                              onClick={() => {
                                setOneEnded(item, index);
                              }}
                            >
                              {MultipleEndBtn(8, 8)}
                            </div>
                          </div>
                          <div style={{ marginTop: 3 }}>
                            {item.status === 'multiple' &&
                            item.callingStatus.length <= 0 ? (
                              <span
                                style={{
                                  color: '#fff',
                                  fontFamily: 'Poppins, sans-serif',
                                  fontSize: 12,
                                  fontWeight: 700,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'flex-end',
                                }}
                              >
                                <FormattedMessage
                                  id="new.worktable.phone.callout"
                                  defaultMessage="正在连接"
                                />
                              </span>
                            ) : item.callingStatus?.includes('keep') ? (
                              <span
                                style={{
                                  color: '#FFD301',
                                  fontFamily: 'Poppins, sans-serif',
                                  fontSize: 12,
                                  fontWeight: 700,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'flex-end',
                                }}
                              >
                                <FormattedMessage
                                  id="new.worktable.phone.callKeep"
                                  defaultMessage="通话保持中"
                                />
                              </span>
                            ) : item.callingStatus?.includes('sil') ? (
                              <span
                                style={{
                                  color: '#FFD301',
                                  fontFamily: 'Poppins, sans-serif',
                                  fontSize: 12,
                                  fontWeight: 700,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'flex-end',
                                }}
                              >
                                <FormattedMessage
                                  id="new.worktable.phone.callSil"
                                  defaultMessage="静音中"
                                />
                              </span>
                            ) : (
                              <span
                                style={{
                                  color: '#13C825',
                                  fontFamily: 'Poppins, sans-serif',
                                  fontSize: 12,
                                  fontWeight: 400,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'flex-end',
                                }}
                              >
                                <FormattedMessage
                                  id="new.worktable.phone.calling"
                                  defaultMessage="正在通话"
                                />
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </Space>
              </div>
            </div>

            <div className={styles.contentBoxPhoneButton}>
              {callingStatus.includes('keep') ? (
                <div
                  className={styles.contentBoxPhoneButtonItemStop}
                  style={{
                    backgroundColor: '#AD30E5',
                  }}
                  onClick={() => onNOKeepCall('multiple')}
                >
                  {CallPlayIcon(25, 25)}
                </div>
              ) : (
                <div
                  className={styles.contentBoxPhoneButtonItemStop}
                  style={{
                    backgroundColor: '#fff',
                  }}
                  onClick={() => onKeepCall('multiple')}
                >
                  {CallKeepIcon(25, 25)}
                </div>
              )}
              {callingStatus.includes('sil') ? (
                <div
                  className={styles.contentBoxPhoneButtonItemSli}
                  style={{
                    backgroundColor: '#AD30E5',
                  }}
                  onClick={() => onNOSilenceCall('multiple')}
                >
                  {CallSlinoIcon(25, 25)}
                </div>
              ) : (
                <div
                  className={styles.contentBoxPhoneButtonItemSli}
                  style={{
                    backgroundColor: '#fff',
                  }}
                  onClick={() => onSilenceCall('multiple')}
                >
                  {CallSliIcon(25, 25)}
                </div>
              )}
              <div
                className={styles.contentBoxPhoneButtonItemYellow}
                onClick={() => {
                  handleCallStatus(true, 'connect');
                  pushUserList('multiple');
                  listQueueQuickConnects();
                }}
              >
                {QuickConnectBtn()}
              </div>
              <div
                className={styles.contentBoxPhoneButtonItemRed}
                onClick={() => destroyCall('multiple')}
              >
                {MultipleEndBtn(25, 25)}
              </div>
            </div>
          </div>
        </Spin>
      </div>
      <div
        id="container-div"
        style={{ width: 400, height: 800, display: 'none' }}
        hidden={ccpLoading}
      ></div>
    </>
  );
});

export default CallComponents;
