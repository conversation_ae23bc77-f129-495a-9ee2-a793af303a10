import React, { useState, useEffect } from 'react';
import { useDispatch, getIntl, history, FormattedMessage } from 'umi';
import styles from './index.less';
import {
  Spin,
  Tabs,
  Select,
  DatePicker,
  Popover,
  Radio,
  Table,
  Checkbox,
  Progress,
  notification,
} from 'antd';
import { NumBalanceIcon, RightArrowIcon } from '../overView/icon';
import AreaChart from './AreaChart';
import Rose<PERSON><PERSON> from './RoseChart';
import {
  queryOuterAgentPercent,
  queryOuterAgentRemainCount,
} from '../../../service/meteringBilling';
import NoDataImg from '../../../assets/no-data-img.jpg';
import { formatThousand } from '../../../utils/utils';

const { RangePicker } = DatePicker;

const ExternalIntelligentAgentAIGCPackage = () => {
  const dispatch = useDispatch();
  const [tabKey, setTabKey] = useState('1');

  // 切换tab
  const onChangeTab = key => {
    console.log(key);
    setTabKey(key);
  };

  return (
    <div className={styles.externalIntelligentAgentAIGCPackage}>
      <p className="blueBorder">
        <FormattedMessage
          id="external.intelligent.agent.AIGC.package.title"
          defaultValue="外部智能体AIGC套餐包"
        />
      </p>
      <Tabs
        activeKey={tabKey}
        onChange={onChangeTab}
        items={[
          {
            label: getIntl().formatMessage({
              id: 'external.intelligent.agent.AIGC.package.data.analysis',
              defaultValue: '数据分析',
            }),
            key: '1',
            children: <DataAnalysisContent tabKey={tabKey} />,
          },
          {
            label: getIntl().formatMessage({
              id: 'external.intelligent.agent.AIGC.package',
              defaultValue: 'AIGC套餐包',
            }),
            key: '2',
            children: <AIGCPackage tabKey={tabKey} />,
          },
        ]}
      />
    </div>
  );
};
// 数据分析
const DataAnalysisContent = props => {
  const dispatch = useDispatch();
  const [spinning, setSpinning] = useState(false);
  const [spinning1, setSpinning1] = useState(false);
  const [spinning2, setSpinning2] = useState(false);
  const [timeList, setTimeList] = useState([
    {
      value: 1,
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.time.one.month',
        defaultValue: '近一个月',
      }),
    },
    {
      value: 2,
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.time.six.month',
        defaultValue: '近六个月',
      }),
    },
    {
      value: 3,
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.time.one.year',
        defaultValue: '近一年',
      }),
    },
    {
      value: 4,
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.time.customize',
        defaultValue: '自定义',
      }),
    },
  ]);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [selectTime, setSelectTime] = useState(false);
  // 使用 useState 来存储日期范围
  const [dates, setDates] = useState(null);
  //时间单位 1-日 2-周 3-月 4-时
  const [timeDimension, setTimeDimension] = useState('2');
  const [timeDifferenceInHours, setTimeDifferenceInHours] = useState('');
  // 时间类型
  const [timeRangeCode, setTimeRangeCode] = useState(1);
  const [outerAgentTrendData, setOuterAgentTrendData] = useState([]); //AIGC调用次数趋势数据
  const [outerAgentPercentData, setOuterAgentPercentData] = useState([]); //外部智能体AIGC各渠道调用次数占比
  const [dataSource, setDataSource] = useState([]); //调用次数明细
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [outerAgentPercentSum, setOuterAgentPercentSum] = useState(''); //外部智能体AIGC各渠道调用次数总数
  const colorList = [
    '#3463FC',
    '#AD30E5',
    '#FCB830',
    '#722ED1',
    '#4FCCFF',
    '#6FE621',
  ];
  // 座席工作效率统计汇总
  useEffect(() => {
    if (!selectTime) {
      const startDateTime = new Date(startDate);
      const endDateTime = new Date(endDate);
      const timeDifferenceInMilliseconds = endDateTime - startDateTime;
      const timeDifferenceInHours1 =
        timeDifferenceInMilliseconds / (1000 * 60 * 60);
      setTimeDifferenceInHours(timeDifferenceInHours1);
    }
  }, [selectTime, endDate]);

  useEffect(() => {
    if (
      (props.tabKey === '1' && timeRangeCode != 4) ||
      (props.tabKey === '1' && timeRangeCode == 4 && startDate && endDate)
    ) {
      queryOuterAgentPercent();
    }
  }, [timeRangeCode, startDate, endDate, props.tabKey]);
  useEffect(() => {
    if (
      (props.tabKey === '1' && timeRangeCode != 4) ||
      (props.tabKey === '1' && timeRangeCode == 4 && startDate && endDate)
    ) {
      queryOuterAgentUseCountDetail();
    }
  }, [timeRangeCode, startDate, endDate, props.tabKey, pageNum, pageSize]);
  useEffect(() => {
    if (
      (props.tabKey === '1' && timeRangeCode != 4) ||
      (props.tabKey === '1' && timeRangeCode == 4 && startDate && endDate)
    ) {
      queryOuterAgentTrend();
    }
  }, [timeDimension, timeRangeCode, startDate, endDate, props.tabKey]);

  // 外部智能体-数据分析-AIGC调用次数趋势图
  const queryOuterAgentTrend = () => {
    setSpinning(true);
    let params = {
      timeRangeCode: timeRangeCode,
      timeDimension: timeDimension,
      startDate: startDate,
      endDate: endDate,
    };
    dispatch({
      type: 'meteringBilling/queryOuterAgentTrend',
      payload: params,
      callback: response => {
        setSpinning(false);
        if (response.code === 200) {
          // 转换为目标格式
          const formattedData = response.data?.timeRangeList.map(
            (timeRange, index) => {
              return {
                Date: timeRange,
                num: response.data?.barDataList[index], // 对应 barDataList 中的值
              };
            },
          );
          setOuterAgentTrendData(formattedData);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 外部智能体-数据分析-AIGC各渠道调用次数占比
  const queryOuterAgentPercent = () => {
    setSpinning1(true);
    let params = {
      timeRangeCode: timeRangeCode,
      timeDimension: timeDimension,
      startDate: startDate,
      endDate: endDate,
    };
    dispatch({
      type: 'meteringBilling/queryOuterAgentPercent',
      payload: params,
      callback: response => {
        setSpinning1(false);
        if (response.code === 200) {
          let data = response.data;
          setOuterAgentPercentData(data);
          let sum = 0;
          data?.forEach(item => {
            sum += item.num;
          });
          setOuterAgentPercentSum(sum);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 外部智能体-数据分析-调用次数明细
  const queryOuterAgentUseCountDetail = () => {
    setSpinning2(true);
    let data = {
      timeRangeCode: timeRangeCode,
      timeDimension: timeDimension,
      startDate: startDate,
      endDate: endDate,
    };
    let params = {
      pageNum: pageNum,
      pageSize: pageSize,
    };
    dispatch({
      type: 'meteringBilling/queryOuterAgentUseCountDetail',
      payload: {
        params: params,
        data: data,
      },
      callback: response => {
        setSpinning2(false);
        if (response.code === 200) {
          setTotal(response.data.total);
          setDataSource(response.data.records);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 选择时间类型
  const handleChange = value => {
    setTimeRangeCode(value);
  };
  // 切换时间范围
  const rangePickerChange = (value, dateString) => {
    setStartDate(dateString[0]);
    setEndDate(dateString[1]);
    setDates(value);
  };
  const onOpenChange = open => {
    setSelectTime(open);
  };
  // 切换时、日、周、月
  const onChangeChart = ({ target: { value } }) => {
    setTimeDimension(value);
  };

  // 选择时间范围小于24小时提示
  const hourContent = (
    <div>
      <p>
        <FormattedMessage
          id="work.efficiency.statistics.select.time.tips.text"
          defaultMessage="选择时间范围需小于24小时"
        />
      </p>
    </div>
  );

  const columns = [
    {
      title: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.table.date',
        defaultValue: '日期',
      }),
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: getIntl().formatMessage({
        id: 'channel.type',
        defaultValue: '渠道类型',
      }),
      dataIndex: 'channelTypeName',
      key: 'channelTypeName',
    },
    {
      title: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.table.channel',
        defaultValue: '渠道',
      }),
      dataIndex: 'channelName',
      key: 'channelName',
    },
    {
      title: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.table.number.calls',
        defaultValue: '调用次数',
      }),
      dataIndex: 'num',
      key: 'num',
      sorter: {
        compare: (a, b) => a.num - b.num,
      },
      render: (text, record) => {
        return (
          <span>
            {record.num}
            <FormattedMessage
              id="external.intelligent.agent.AIGC.package.unit.times"
              defaultMessage="次"
            />
          </span>
        );
      },
    },
    {
      title: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.table.billing.frequency',
        defaultValue: '计费次数',
      }),
      dataIndex: 'billTimes',
      key: 'billTimes',
      sorter: {
        compare: (a, b) => a.billTimes - b.billTimes,
      },
      render: (text, record) => {
        return (
          <span>
            {record.billTimes}
            <FormattedMessage
              id="external.intelligent.agent.AIGC.package.unit.times"
              defaultMessage="次"
            />
          </span>
        );
      },
    },
  ];

  return (
    <div className={styles.dataAnalysisContainer}>
      <div className={styles.selectContainer}>
        <span className={styles.labelText}>
          <FormattedMessage
            id="external.intelligent.agent.AIGC.package.select.time"
            defaultMessage="选择时间"
          />
        </span>
        <Select
          value={timeRangeCode}
          style={{ width: '25%' }}
          onChange={handleChange}
          options={timeList}
          placeholder={getIntl().formatMessage({
            id:
              'external.intelligent.agent.AIGC.package.select.time.placeholder',
            defaultValue: '请选择时间',
          })}
        />
        {timeRangeCode === 4 && (
          <>
            <span className={styles.labelText}>
              <FormattedMessage
                id="external.intelligent.agent.AIGC.package.time.frame"
                defaultMessage="时间范围"
              />
            </span>
            <RangePicker
              allowClear={false}
              onChange={rangePickerChange}
              onOpenChange={onOpenChange}
              value={dates}
              // 显示时分秒
              showTime={{
                format: 'HH:mm:ss',
              }}
            />
          </>
        )}
      </div>
      <div className={styles.topContainer}>
        <div className={styles.topLeftContainer}>
          <Spin spinning={spinning}>
            <div className={styles.secondTitle} style={{ height: '30px' }}>
              <span>
                <FormattedMessage
                  id="external.intelligent.agent.AIGC.package.call.frequency.trend.chart"
                  defaultMessage="外部智能体AIGC计费次数趋势图"
                />
              </span>
              <Radio.Group
                style={{ marginRight: '10px' }}
                defaultValue={timeDimension}
                onChange={onChangeChart}
              >
                <Popover
                  overlayClassName="agentWorkEfficiencyStatisticsPopover"
                  content={
                    timeDifferenceInHours < 24 &&
                    timeRangeCode === 4 &&
                    timeDifferenceInHours > 0
                      ? null
                      : hourContent
                  }
                  title={null}
                >
                  <Radio.Button
                    value="4"
                    disabled={
                      timeDifferenceInHours < 24 &&
                      timeRangeCode === 4 &&
                      timeDifferenceInHours > 0
                        ? ''
                        : 'true'
                    }
                  >
                    <FormattedMessage
                      id="agent.work.efficiency.statistics.hour.text"
                      defaultMessage="时"
                    />
                  </Radio.Button>
                </Popover>
                <Radio.Button value="1">
                  <FormattedMessage
                    id="agent.work.efficiency.statistics.day.text"
                    defaultMessage="日"
                  />
                </Radio.Button>
                <Radio.Button value="2">
                  <FormattedMessage
                    id="agent.work.efficiency.statistics.week.text"
                    defaultMessage="周"
                  />
                </Radio.Button>
                <Radio.Button value="3">
                  <FormattedMessage
                    id="agent.work.efficiency.statistics.month.text"
                    defaultMessage="月"
                  />
                </Radio.Button>
              </Radio.Group>
            </div>
            {outerAgentTrendData?.length > 0 ? (
              <AreaChart data={outerAgentTrendData} />
            ) : (
              <div className={styles.noDataContent}>
                <img src={NoDataImg} />
                <p>
                  <FormattedMessage
                    id="work.order.reply.no.data"
                    defaultMessage="暂无数据"
                  />
                </p>
              </div>
            )}
          </Spin>
        </div>
        <div className={styles.topRightContainer}>
          <Spin spinning={spinning1}>
            <div className={styles.secondTitle} style={{ height: '30px' }}>
              <FormattedMessage
                id="external.intelligent.agent.AIGC.package.proportion.channel.call.frequency"
                defaultMessage="外部智能体AIGC各渠道计费次数占比"
              />
            </div>
            {outerAgentPercentData?.length > 0 ? (
              <div style={{ height: '225px', marginTop: '10px' }}>
                <div className={styles.roseChart}>
                  <RoseChart data={outerAgentPercentData} />
                </div>
                <div className={styles.dataContainer}>
                  {outerAgentPercentData?.map((item, index) => {
                    return (
                      <div className={styles.dataItem}>
                        <div
                          className={styles.circle}
                          style={{
                            background: colorList[index]
                              ? colorList[index]
                              : colorList[index % 6],
                          }}
                        ></div>
                        <div className={styles.channelName}>
                          {item.groupName}
                        </div>
                        <div
                          className={styles.numText}
                          style={{
                            color: colorList[index]
                              ? colorList[index]
                              : colorList[index % 6],
                          }}
                        >
                          {((item.num / outerAgentPercentSum) * 100).toFixed(
                            1,
                          ) + '%'}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className={styles.noDataContent}>
                <img src={NoDataImg} />
                <p>
                  <FormattedMessage
                    id="work.order.reply.no.data"
                    defaultMessage="暂无数据"
                  />
                </p>
              </div>
            )}
          </Spin>
        </div>
      </div>
      <div className={styles.bottomContainer}>
        <p className={styles.secondTitle}>
          <FormattedMessage
            id="external.intelligent.agent.AIGC.package.call.frequency.details"
            defaultMessage="调用次数明细"
          />
        </p>
        <Table
          dataSource={dataSource}
          columns={columns}
          loading={spinning2}
          pagination={{
            total: total,
            pageSize: pageSize,
            current: pageNum,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showTotal: total => (
              <FormattedMessage
                id="page.total.num"
                defaultMessage={`共 ${total} 条`}
                values={{ total }}
              />
            ),
            onChange: (pageNum, pageSize) => {
              setPageNum(pageNum);
              setPageSize(pageSize);
            },
          }}
        />
      </div>
    </div>
  );
};

// AIGC套餐包
const AIGCPackage = props => {
  const dispatch = useDispatch();
  const [spinning, setSpinning] = useState(false);
  const [loadingTable, setLoadingTable] = useState(false);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [selectTime, setSelectTime] = useState(false);
  const [remainCount, setRemainCount] = useState('');
  const [remainList, setRemainList] = useState([]);

  // 使用 useState 来存储日期范围
  const [dates, setDates] = useState(null);
  //状态维度 0-所有（默认） 1-生效中 2-已过期 3-已耗尽
  const [statusCode, setStatusCode] = useState(0);
  const optionsCheckBox = [
    {
      label: getIntl().formatMessage({
        id: 'home.set.options.1',
        defaultValue: '全部',
      }),
      value: 0,
    },
    {
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.status.in.effect',
        defaultValue: '生效中',
      }),
      value: 1,
    },
    {
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.status.expired',
        defaultValue: '已过期',
      }),
      value: 2,
    },
    {
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.AIGC.package.select.status.exhausted',
        defaultValue: '已耗尽',
      }),
      value: 3,
    },
  ];

  useEffect(() => {
    if (props.tabKey === '2') {
      queryOuterAgentRemainCount();
    }
  }, [props.tabKey]);

  useEffect(() => {
    if (props.tabKey === '2') {
      queryOuterAgentDetail();
    }
  }, [startDate, endDate, statusCode, props.tabKey, pageNum, pageSize]);

  // 外部智能体-AIGC套餐包-剩余可调用次数
  const queryOuterAgentRemainCount = () => {
    dispatch({
      type: 'meteringBilling/queryOuterAgentRemainCount',
      callback: response => {
        if (response.code === 200) {
          setRemainCount(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 外部智能体-AIGC套餐包-分页查询列表信息
  const queryOuterAgentDetail = () => {
    setLoadingTable(true);
    let data = {
      startDate: startDate,
      endDate: endDate,
      statusCode: statusCode,
    };
    let params = {
      pageNum: pageNum,
      pageSize: pageSize,
    };
    dispatch({
      type: 'meteringBilling/queryOuterAgentDetail',
      payload: {
        params: params,
        data: data,
      },
      callback: response => {
        setLoadingTable(false);
        if (response.code === 200) {
          setTotal(response.data.total);
          let records = response.data.records?.map(item => {
            if (
              new Date(item.expiryDateEnd?.replace(' ', 'T') + ':00') <
              new Date()
            ) {
              return {
                ...item,
                status: '已过期',
              };
            } else if (
              new Date(item.expiryDateEnd?.replace(' ', 'T') + ':00') >
                new Date() &&
              new Date(item.expiryDateBegin?.replace(' ', 'T') + ':00') <
                new Date() &&
              Number(item.remainNum) === 0
            ) {
              return {
                ...item,
                status: '已耗尽',
              };
            } else if (
              new Date(item.expiryDateEnd?.replace(' ', 'T') + ':00') >
                new Date() &&
              new Date(item.expiryDateBegin?.replace(' ', 'T') + ':00') <
                new Date() &&
              Number(item.remainNum) > 0
            ) {
              return {
                ...item,
                status: '生效中',
              };
            }
          });
          setRemainList(records);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 切换时间范围
  const rangePickerChange = (value, dateString) => {
    setStartDate(dateString[0]);
    setEndDate(dateString[1]);
    setDates(value);
  };
  const onOpenChange = open => {
    setSelectTime(open);
  };

  // 切换状态
  const onChangeCheckBox = checkedValues => {
    setStatusCode(checkedValues);
  };

  const columns = [
    {
      title: (
        <span style={{ fontWeight: 'bolder' }}>
          {getIntl().formatMessage({
            id: 'external.intelligent.agent.AIGC.package.table.type',
            defaultValue: '类型',
          })}
        </span>
      ),
      dataIndex: 'type',
      key: 'type',
      render: (text, record) => {
        return (
          <div className={styles.circleContainer}>
            {record.type === 1
              ? getIntl().formatMessage({
                  id: 'agent.AIGC.package.table.type.1',
                  defaultValue: '系统赠送',
                })
              : getIntl().formatMessage({
                  id: 'agent.AIGC.package.table.type.2',
                  defaultValue: '企业购买',
                })}
          </div>
        );
      },
    },
    {
      title: (
        <span style={{ fontWeight: 'bolder' }}>
          {getIntl().formatMessage({
            id: 'external.intelligent.agent.AIGC.package.select.buy.time',
            defaultValue: '购买时间',
          })}
        </span>
      ),
      dataIndex: 'expiryDateBegin',
      key: 'expiryDateBegin',
      sorter: (a, b) =>
        new Date(a.expiryDateBegin) - new Date(b.expiryDateBegin),
    },
    {
      title: (
        <span style={{ fontWeight: 'bolder' }}>
          {getIntl().formatMessage({
            id: 'external.intelligent.agent.AIGC.package.table.expiration.date',
            defaultValue: '到期时间',
          })}
        </span>
      ),
      dataIndex: 'expiryDateEnd',
      key: 'expiryDateEnd',
      sorter: (a, b) => new Date(a.expiryDateEnd) - new Date(b.expiryDateEnd),
    },
    {
      title: (
        <span style={{ fontWeight: 'bolder' }}>
          {getIntl().formatMessage({
            id: 'external.intelligent.agent.AIGC.package.table.number.calls',
            defaultValue: '调用次数',
          })}
        </span>
      ),
      dataIndex: 'useNum',
      key: 'useNum',
      minWidth: 400,
      sorter: {
        compare: (a, b) => a.useNum - b.useNum,
      },
      render: (text, record) => {
        return (
          <div className={styles.numberCallsContainer}>
            <div className={styles.progressContainer}>
              <div
                className={styles.progressInnerContainer}
                style={{
                  background:
                    record.status === '生效中'
                      ? '#13c825'
                      : record.status === '已过期'
                      ? '#999999'
                      : '#F22417',
                  width:
                    (
                      ((Number(record.totalNum) - Number(record.remainNum)) /
                        record.totalNum) *
                      100
                    ).toFixed(2) + '%',
                }}
              ></div>
            </div>
            <span
              className={styles.numText1}
              style={{
                color:
                  record.status === '生效中'
                    ? '#13c825'
                    : record.status === '已过期'
                    ? '#999999'
                    : '#F22417',
              }}
            >
              {formatThousand(record.useNum)}
            </span>
            <span className={styles.numText2}>
              / {formatThousand(record.totalNum)}
              <FormattedMessage
                id="external.intelligent.agent.AIGC.package.unit.times"
                defaultMessage="次"
              />
            </span>
          </div>
        );
      },
    },
    {
      title: (
        <span style={{ fontWeight: 'bolder' }}>
          {getIntl().formatMessage({
            id: 'external.intelligent.agent.AIGC.package.table.status',
            defaultValue: '状态',
          })}
        </span>
      ),
      dataIndex: 'status',
      key: 'status',
      fixed: 'right',
      render: (text, record) => {
        return (
          <div className={styles.circleContainer}>
            {record.status === '生效中' ? (
              <>
                <div className={styles.circle}></div>
                <span>
                  {getIntl().formatMessage({
                    id:
                      'external.intelligent.agent.AIGC.package.select.status.in.effect',
                    defaultValue: '生效中',
                  })}
                </span>
              </>
            ) : record.status === '已过期' ? (
              <>
                <div className={styles.circle1}></div>
                <span>
                  {getIntl().formatMessage({
                    id:
                      'external.intelligent.agent.AIGC.package.select.status.expired',
                    defaultValue: '已过期',
                  })}
                </span>
              </>
            ) : record.status === '已耗尽' ? (
              <>
                <div className={styles.circle2}></div>
                <span>
                  {getIntl().formatMessage({
                    id:
                      'external.intelligent.agent.AIGC.package.select.status.exhausted',
                    defaultValue: '已耗尽',
                  })}
                </span>
              </>
            ) : (
              ''
            )}
          </div>
        );
      },
    },
  ];

  return (
    <div className={styles.aigcPackageContainer}>
      <div className={styles.balanceContainer}>
        <div className={styles.iconContainer}>{NumBalanceIcon()}</div>
        <div className={styles.balanceNumContainer}>
          <p>
            <span className={styles.currentBalance}>
              <FormattedMessage
                id="external.intelligent.agent.AIGC.package.remaining.callable.times"
                defaultMessage="剩余可调用次数"
              />
            </span>
            {/* <span className={styles.goRecharge}>
              <span>
                <FormattedMessage
                  id="external.intelligent.agent.AIGC.package.go.buy"
                  defaultMessage="去购买"
                />
              </span>
              {RightArrowIcon()}
            </span> */}
          </p>
          <div>
            <span className={styles.balanceNum}>
              {formatThousand(remainCount)}
            </span>
            <span className={styles.unitYuan}>
              <FormattedMessage
                id="external.intelligent.agent.AIGC.package.unit.times"
                defaultMessage="次"
              />
            </span>
          </div>
        </div>
      </div>
      <div className={styles.selectContainer}>
        <span className={styles.labelText}>
          <FormattedMessage
            id="external.intelligent.agent.AIGC.package.select.buy.time"
            defaultMessage="购买时间"
          />
        </span>
        <RangePicker
          allowClear={true}
          onChange={rangePickerChange}
          onOpenChange={onOpenChange}
          value={dates}
          // 显示时分秒
          showTime={{
            format: 'HH:mm:ss',
          }}
          cl
        />
        <span className={styles.labelText}>
          <FormattedMessage
            id="external.intelligent.agent.AIGC.package.select.status"
            defaultMessage="状态："
          />
        </span>
        <Select
          options={optionsCheckBox}
          onChange={onChangeCheckBox}
          value={statusCode}
        />
      </div>
      <div className={styles.tableContainer}>
        <Table
          dataSource={remainList}
          columns={columns}
          loading={setLoadingTable}
          pagination={{
            total: total,
            pageSize: pageSize,
            current: pageNum,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showTotal: total => (
              <FormattedMessage
                id="page.total.num"
                defaultMessage={`共 ${total} 条`}
                values={{ total }}
              />
            ),
            onChange: (pageNum, pageSize) => {
              setPageNum(pageNum);
              setPageSize(pageSize);
            },
          }}
        />
      </div>
    </div>
  );
};

export default ExternalIntelligentAgentAIGCPackage;
