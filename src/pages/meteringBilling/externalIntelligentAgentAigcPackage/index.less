.externalIntelligentAgentAIGCPackage {
  margin: 20px;
  background: #fff;
  padding: 20px;

  .dataAnalysisContainer {
    width: 100%;

    .selectContainer {
      width: 100%;
      height: 35px;
      margin-bottom: 20px;

      div {
        float: left;
      }

      .labelText {
        float: left;
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 32px;
        /* 18px */
        margin-right: 5px;
      }

      :global {
        .ant-select {
          font-size: 12px;
          color: #333;
          margin-right: 20px;
          box-shadow: none;
        }

        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          border-radius: 6px;
          background: rgba(255, 255, 255, 0.5);
          box-shadow: none;
        }

        .ant-picker {
          border-radius: 6px;
          background: rgba(255, 255, 255, 0.5);
          box-shadow: none;
        }
      }
    }

    .topContainer {
      width: 100%;
      height: 300px;
      margin-bottom: 20px;

      .topLeftContainer {
        width: 55%;
        height: 300px;
        border-radius: 4px;
        background: #f9fbff;
        float: left;
        margin-right: 1%;
        padding: 10px;

        .noDataContent {
          width: 70%;
          height: 240px;
          margin: auto;
          display: grid;
          justify-content: center;
          align-content: center;

          img {
            width: 80%;
            margin-left: 10%;
          }

          p {
            color: #999;
            margin-top: 10px;
            text-align: center;
          }
        }

        :global {
          .ant-radio-group {
            float: right;
            border: 1px solid #3968fc;
            border-radius: 4px;
          }

          .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
            background-color: #fff;
          }

          .ant-radio-button-wrapper:not(:first-child)::before {
            width: 0px;
          }

          .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
            border: none;
            box-shadow: none;
            //border-radius:4px 0px 0px 4px;
            background: rgba(52, 99, 252, 0.2);
          }

          .ant-radio-button-wrapper {
            border: none;
            box-shadow: none;
          }

          .ant-radio-button-wrapper {
            height: 30px;
            color: #666;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
          }

          .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
            color: #3463fc;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
          }
        }
      }

      .topRightContainer {
        width: 44%;
        height: 300px;
        border-radius: 4px;
        background: #f9fbff;
        float: left;
        padding: 10px;

        .roseChart {
          width: 50%;
          float: left;
        }

        .dataContainer {
          width: 50%;
          float: left;
          height: 235px;
          overflow: hidden;
          overflow-y: scroll;

          .dataItem {
            width: 95%;
            height: 30px;
            margin-bottom: 12px;
            flex-shrink: 0;
            background: rgba(52, 99, 252, 0.02);
            padding: 7px 12px;
            display: flex;
            align-items: center;

            .circle {
              width: 7px;
              height: 7px;
              background: #3463fc;
              border-radius: 50%;
              margin-right: 12px;
            }

            .channelName {
              color: #333;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: normal;
              flex: auto;
            }

            .numText {
              color: #3463fc;
              text-align: right;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: normal;
              text-transform: uppercase;
              float: right;
            }

            .circle1 {
              width: 7px;
              height: 7px;
              background: #ad30e5;
              border-radius: 50%;
              margin-right: 12px;
            }

            .numText1 {
              color: #ad30e5;
              text-align: right;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: normal;
              text-transform: uppercase;
              float: right;
            }

            .circle2 {
              width: 7px;
              height: 7px;
              background: #fcb830;
              border-radius: 50%;
              margin-right: 12px;
            }

            .numText2 {
              color: #fcb830;
              text-align: right;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: normal;
              text-transform: uppercase;
              float: right;
            }
          }
        }

        .noDataContent {
          width: 70%;
          height: 240px;
          margin: auto;
          display: grid;
          justify-content: center;
          align-content: center;

          img {
            width: 80%;
            margin-left: 10%;
          }

          p {
            color: #999;
            margin-top: 10px;
            text-align: center;
          }
        }
      }
    }

    .bottomContainer {
      width: 100%;

      :global {
        .ant-table-thead {
          .ant-table-cell {
            font-weight: 700 !important;
          }
        }
      }
    }

    .secondTitle {
      color: #333;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .aigcPackageContainer {
    .balanceContainer {
      width: 100%;
      padding: 10px;
      border-radius: 4px;
      background: #f5f7ff;
      margin-bottom: 20px;
      height: 80px;

      .iconContainer {
        width: 60px;
        float: left;
        margin-right: 10px;
      }

      .balanceNumContainer {
        float: left;

        p {
          line-height: 22px;
          margin-bottom: 12px;
        }

        .currentBalance {
          color: #999;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%;
          /* 18px */
        }

        .goRecharge {
          color: #3463fc;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%;
          /* 18px */
          margin-left: 20px;
          cursor: pointer;
        }

        .balanceNum {
          color: #3463fc;
          font-size: 20px;
          font-style: normal;
          font-weight: 700;
          line-height: 150%;
          /* 30px */
        }

        .unitYuan {
          color: #3463fc;
          font-size: 12px;
          font-style: normal;
          font-weight: 700;
          line-height: 150%;
          margin-left: 2px;
        }
      }
    }

    .selectContainer {
      width: 100%;
      height: 35px;
      margin-bottom: 10px;

      .labelText {
        float: left;
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 32px;
        /* 18px */
        margin-right: 5px;
      }

      :global {
        .ant-picker {
          float: left;
          border-radius: 6px;
          background: rgba(255, 255, 255, 0.5);
          margin-right: 20px;
          box-shadow: none;
        }

        .ant-select-selector {
          border-radius: 6px;
          font-size: 12px;
          box-shadow: none;
        }

        .ant-checkbox-group {
          line-height: 32px;
        }

        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          box-shadow: none;
        }

        .ant-checkbox-checked .ant-checkbox-inner {
          background-color: #3463fc;
          border-color: #3463fc;
        }
      }
    }

    .tableContainer {
      width: 100%;

      .numberCallsContainer {
        .progressContainer {
          width: 227px;
          height: 20px;
          padding: 4px;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;
          gap: 10px;
          border-radius: 4px;
          background: #f3f7fe;
          float: left;

          .progressInnerContainer {
            width: 157px;
            height: 12px;
            flex-shrink: 0;
            border-radius: 2px;
            background: #13c825;
          }
        }

        .numText1 {
          color: #13c825;
          text-align: center;
          font-size: 12px;
          font-style: normal;
          font-weight: 700;
          line-height: 22px;
          /* 183.333% */
          margin-left: 12px;
          margin-right: 2px;
        }

        .numText2 {
          color: #333;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
      }

      .circleContainer {
        display: flex;
        align-items: center;
        gap: 3px;

        .circle {
          width: 8px;
          height: 8px;
          background: #13c825;
          border-radius: 50%;
          float: left;
          margin-right: 3px;
        }

        .circle1 {
          width: 8px;
          height: 8px;
          background: #999;
          border-radius: 50%;
          float: left;
          margin-right: 3px;
        }

        .circle2 {
          width: 8px;
          height: 8px;
          background: #f22417;
          border-radius: 50%;
          float: left;
          margin-right: 3px;
        }
      }
    }
  }

  :global {
    .ant-tabs-tab {
      border-radius: 4px 4px 0px 0px;
      border-top: 1px solid #e6e6e6;
      border-right: 1px solid #e6e6e6;
      border-left: 1px solid #e6e6e6;
      background: rgba(230, 230, 230, 0.2);
      padding: 7px 12px;

      color: #333;
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
      /* 21px */
    }

    .ant-tabs-tab.ant-tabs-tab-active {
      border-radius: 4px 4px 0px 0px;
      background: #3463fc;
      color: #fff;
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: 150%;
      /* 21px */
    }

    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #fff;
      text-shadow: none;
    }

    .ant-tabs-ink-bar {
      display: none;
    }

    .ant-tabs-tab + .ant-tabs-tab {
      margin-left: 10px;
    }
  }
}
