import React, { Component } from 'react';
import { connect, FormattedMessage, history, getIntl } from 'umi';
import { Input, Button, Checkbox, Spin, Select, Tabs, Table } from 'antd';
import styles from './index.less';
import { SearchOutlined } from '@ant-design/icons';
import { notification } from '../../utils/utils';
import WhatsAppIcon from '@/assets/whats-app.svg';
import { checkIcon } from './icon';
import { areaNumList } from '@/pages/AIAgentBox/components/canvasFlow/AreaCode';

class MarketingDetailsContent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loadingDetail: false,
      workOrderStatus: '',
      activityList: [],
      channelTypeContactList: [],
      eventNameList: [],
      eventBatchList: [],
      channelType: '4',
      activityId: '',
      eventId: '',
      batchNum: '',
      marketingType: '',
      customerContactInfo: '',
      selectItem: {
        activityId: '',
        channelType: '4',
        eventId: '',
        batchNum: '',
        marketingType: '',
        customerContactInfo: '',
        sendStatus: '',
      },
      sendStatus: '',
      total: 0,
      pageSize: 10,
      pageNum: 1,
      marketingDetailList: [],
      allNum: 0,
      sentNum: 0,
      deliveryNum: 0,
      openNum: 0,
      clickNum: 0,
      complaintNum: 0,
      unsubscribeNum: 0,
      bounceNum: 0,
      deliveryDelayNum: 0,
      rejectNum: 0,
      renderingFailureNum: 0,
      countryCode: '+86',
      phoneNumber: '',
    };
  }
  componentDidMount() {
    if (this.props.history.location.state) {
      let {
        channelType,
        marketingType,
        customerContactInfo,
        sendStatus,
        eventId,
        batchNum,
      } = this.state;
      let newActivityId = this.props.history.location.state.activityId;
      let newEventId = this.props.history.location.state.eventId;
      let newBatchNum = this.props.history.location.state.batchNum;
      if (newActivityId) {
        if (newEventId) {
          if (newBatchNum) {
            let params = {
              activityId: newActivityId,
              channelType: channelType,
              eventId: newEventId,
              batchNum: newBatchNum,
              marketingType: marketingType,
              customerContactInfo: customerContactInfo,
              sendStatus: sendStatus,
            };
            this.queryActivityList();
            this.queryEventNameList(newActivityId);
            this.queryEventBatchList(newEventId);
            this.queryMarketingStatusSummary(params);
            this.queryMarketingDetailList(params);
            this.setState({
              activityId: newActivityId,
              eventId: newEventId,
              batchNum: newBatchNum,
            });
          } else {
            let params = {
              activityId: newActivityId,
              channelType: channelType,
              eventId: newEventId,
              batchNum: batchNum,
              marketingType: marketingType,
              customerContactInfo: customerContactInfo,
              sendStatus: sendStatus,
            };
            this.queryActivityList();
            this.queryEventNameList(newActivityId);
            this.queryEventBatchList(newEventId);
            this.queryMarketingStatusSummary(params);
            this.queryMarketingDetailList(params);
            this.setState({
              activityId: newActivityId,
              eventId: newEventId,
            });
          }
        } else {
          let params = {
            activityId: newActivityId,
            channelType: channelType,
            eventId: eventId,
            batchNum: batchNum,
            marketingType: marketingType,
            customerContactInfo: customerContactInfo,
            sendStatus: sendStatus,
          };
          this.queryActivityList();
          this.queryEventNameList(eventId);
          this.queryEventBatchList(batchNum);
          this.queryMarketingStatusSummary(params);
          this.queryMarketingDetailList(params);
          this.setState({
            activityId: newActivityId,
          });
        }
      }
    } else {
      let { selectItem } = this.state;
      this.queryActivityList();
      this.queryEventNameList('');
      this.queryEventBatchList('');
      this.queryMarketingStatusSummary(selectItem);
      this.queryMarketingDetailList(selectItem);
    }
    this.queryChannelTypeContact(); // 新增调用
  }

  // 查询活动名称
  queryActivityList = () => {
    this.props.dispatch({
      type: 'statisticalResults/queryActivityList',
      payload: { excludeEndStatus: 0 },
      callback: response => {
        if (response.code == 200) {
          this.setState({
            activityList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询营销事件
  queryEventNameList = params => {
    this.props.dispatch({
      type: 'statisticalResults/queryEventNameList',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.setState({
            eventNameList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询营销事件批次
  queryEventBatchList = params => {
    this.props.dispatch({
      type: 'statisticalResults/queryEventBatchList',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.setState({
            eventBatchList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询营销事件详情tab
  queryMarketingStatusSummary = selectItem => {
    this.setState({
      loadingDetail: true,
    });
    let { pageSize, pageNum } = this.state;
    let params = {
      selectItem: selectItem,
      pageSize: pageSize,
      pageNum: pageNum,
    };
    this.props.dispatch({
      type: 'statisticalResults/queryMarketingStatusSummary',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          let data = response.data;
          if (data) {
            for (let i = 0; i < data.length; i++) {
              if (data[i].statusCode == 0) {
                this.setState({
                  allNum: data[i].count,
                });
              } else if (data[i].statusCode == 1) {
                this.setState({
                  deliveryNum: data[i].count,
                });
              } else if (data[i].statusCode == 2) {
                this.setState({
                  openNum: data[i].count,
                });
              } else if (data[i].statusCode == 3) {
                this.setState({
                  clickNum: data[i].count,
                });
              } else if (data[i].statusCode == 4) {
                this.setState({
                  complaintNum: data[i].count,
                });
              } else if (data[i].statusCode == 5) {
                this.setState({
                  unsubscribeNum: data[i].count,
                });
              } else if (data[i].statusCode == 6) {
                this.setState({
                  bounceNum: data[i].count,
                });
              } else if (data[i].statusCode == 7) {
                this.setState({
                  deliveryDelayNum: data[i].count,
                });
              } else if (data[i].statusCode == 8) {
                this.setState({
                  rejectNum: data[i].count,
                });
              } else if (data[i].statusCode == 9) {
                this.setState({
                  renderingFailureNum: data[i].count,
                });
              } else if (data[i].statusCode == 10) {
                this.setState({
                  sentNum: data[i].count,
                });
              }
            }
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
        this.setState({
          loadingDetail: false,
        });
      },
    });
  };
  // 查询营销事件列表
  queryMarketingDetailList = selectItem => {
    this.setState({
      loadingDetail: true,
    });
    let { pageSize, pageNum } = this.state;
    let params = {
      selectItem: selectItem,
      pageSize: pageSize,
      pageNum: pageNum,
    };
    this.props.dispatch({
      type: 'statisticalResults/queryMarketingDetailList',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          let data = response.data;
          this.setState({
            marketingDetailList: data.rows,
            total: response.data.total,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
        this.setState({
          loadingDetail: false,
        });
      },
    });
  };

  // 新增：获取渠道类型数据
  queryChannelTypeContact = () => {
    this.props.dispatch({
      type: 'channel/queryChannelTypeContact',
      payload: { channelType: '4' },
      callback: response => {
        if (response && response.code === 200) {
          this.setState({ channelTypeContactList: response.data });
        } else {
          notification.error({ message: response?.msg || '获取渠道类型失败' });
        }
      },
    });
  };

  // 切换活动名称
  handleChangeActivityName = value => {
    if (value !== undefined) {
      this.setState({
        activityId: value,
        eventId: '',
        batchNum: '',
      });
      this.queryEventNameList(value);
      this.queryEventBatchList('');
    } else {
      this.setState({
        activityId: '',
        eventId: '',
        batchNum: '',
      });
      this.queryEventNameList('');
      this.queryEventBatchList('');
    }
  };

  // 切换营销事件
  handleChangeEventName = value => {
    if (value !== undefined) {
      this.setState({
        eventId: value,
        batchNum: '',
      });
      this.queryEventBatchList(value);
    } else {
      this.setState({
        eventId: '',
        batchNum: '',
      });
      this.queryEventBatchList('');
    }
  };
  // 切换营销事件批次
  handleChangeEventBatch = value => {
    if (value !== undefined) {
      this.setState({
        batchNum: value,
      });
    } else {
      this.setState({
        batchNum: '',
      });
    }
  };
  // 切换营销方式
  handleChangeMarketingType = value => {
    if (value !== undefined) {
      this.setState({
        marketingType: value,
      });
    } else {
      this.setState({
        marketingType: '',
      });
    }
  };
  // 联系方式取值
  handleChangeCustomerContactInfo = e => {
    this.setState({
      customerContactInfo: e.target.value,
    });
  };

  // 切换结果tab
  changeTabOrder = key => {
    let {
      activityId,
      channelType,
      eventId,
      batchNum,
      marketingType,
      customerContactInfo,
    } = this.state;
    this.setState(
      {
        pageNum: 1,
      },
      () => {
        if (key == 'all') {
          let params = {
            activityId: activityId,
            channelType: channelType,
            eventId: eventId,
            batchNum: batchNum,
            marketingType: marketingType,
            customerContactInfo: customerContactInfo,
            sendStatus: '',
          };
          this.queryMarketingStatusSummary(params);
          this.queryMarketingDetailList(params);

          this.setState({
            sendStatus: '',
            selectItem: params,
          });
        } else {
          let params = {
            activityId: activityId,
            channelType: channelType,
            eventId: eventId,
            batchNum: batchNum,
            marketingType: marketingType,
            customerContactInfo: customerContactInfo,
            sendStatus: key,
          };
          this.queryMarketingStatusSummary(params);
          this.queryMarketingDetailList(params);
          this.setState({
            sendStatus: key,
            selectItem: params,
          });
        }
      },
    );
  };
  // 点击按钮筛选
  handleSearch = () => {
    let {
      activityId,
      channelType,
      eventId,
      batchNum,
      marketingType,
      sendStatus,
      countryCode,
      phoneNumber,
    } = this.state;
    // 拼接手机号
    let customerContactInfo = '';
    if (countryCode && phoneNumber) {
      customerContactInfo = `${countryCode}${phoneNumber}`;
    }
    this.setState(
      {
        pageNum: 1,
      },
      () => {
        let params = {
          activityId: activityId,
          channelType: channelType,
          eventId: eventId,
          batchNum: batchNum,
          marketingType: marketingType,
          customerContactInfo: customerContactInfo,
          sendStatus: sendStatus,
        };
        this.setState({
          selectItem: params,
        });
        this.queryMarketingStatusSummary(params);
        this.queryMarketingDetailList(params);
      },
    );
  };
  // 导出客户清单
  handleExportCustomer = () => {
    let {
      activityId,
      channelType,
      eventId,
      batchNum,
      marketingType,
      customerContactInfo,
      sendStatus,
    } = this.state;
    let params = {
      activityId: activityId,
      eventId: eventId,
      batchNum: batchNum,
      channelType: channelType,
      sendStatus: sendStatus,
      marketingType: marketingType,
      customerContactInfo: customerContactInfo,
    };
    this.props.dispatch({
      type: 'statisticalResults/exportCustomerList',
      payload: params,
    });
  };

  onChangeStore = pagination => {
    let pageSize = pagination.pageSize;
    let pageNum = pagination.current;
    this.setState(
      {
        pageSize: pageSize,
        pageNum: pageNum,
      },
      () => {
        let selectItem = this.state.selectItem;
        // this.queryMarketingStatusSummary(selectItem);
        this.queryMarketingDetailList(selectItem);
      },
    );
  };

  jumpToDetail = detailId => {
    localStorage.setItem('detailId', detailId);
    history.push({
      pathname: 'marketingResultsDetails',
      state: {
        detailId: detailId,
      },
    });
  };

  render() {
    let {
      loadingDetail,
      total,
      pageSize,
      pageNum,
      workOrderStatus,
      activityList,
      channelTypeContactList,
      eventNameList,
      eventBatchList,
      batchNum,
      eventId,
      allNum,
      sentNum,
      deliveryNum,
      openNum,
      clickNum,
      complaintNum,
      unsubscribeNum,
      bounceNum,
      deliveryDelayNum,
      rejectNum,
      renderingFailureNum,
      marketingDetailList,
      activityId,
    } = this.state;

    const columns = [
      // 1. 营销渠道类型
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.marketing.channel.type',
          defaultValue: '营销渠道类型',
        }),
        dataIndex: 'channelType',
        key: 'channelType',
        render: (text, record) => {
          return (
            <div className={styles.marketingChannelTypeContent}>
              <img src={WhatsAppIcon} />
              <span>
                <FormattedMessage
                  id="marketing.channel.type.whats.app"
                  defaultMessage="WhatsApp"
                />
              </span>
            </div>
          );
        },
      },
      // 2. 渠道名称
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.send.channel',
          defaultValue: '渠道名称',
        }),
        dataIndex: 'channelConfigName',
        key: 'channelConfigName',
        ellipsis: true,
      },
      // 3. 活动名称
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.activity.name',
          defaultValue: '活动名称',
        }),
        dataIndex: 'activityName',
        key: 'activityName',
        ellipsis: true,
        width: 180,
      },
      // 4. 营销智能体
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.marketing.agent',
          defaultValue: '营销智能体',
        }),
        dataIndex: 'agentName',
        key: 'agentName',
        ellipsis: true,
      },
      // 5. 营销事件批次
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.marketing.event.batches',
          defaultValue: '营销事件批次',
        }),
        dataIndex: 'batchNum',
        key: 'batchNum',
        ellipsis: true,
        width: 150,
      },
      // 6. 客户名称
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.customer.name',
          defaultValue: '客户名称',
        }),
        dataIndex: 'customerName',
        key: 'customerName',
        ellipsis: true,
      },
      // 7. 客户联系方式
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.customer.contact.information',
          defaultValue: '客户联系方式',
        }),
        dataIndex: 'customerContactInfo',
        key: 'customerContactInfo',
        width: 180,
        ellipsis: true,
      },
      // 8. 已发送
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.have.send',
          defaultValue: '已发送',
        }),
        dataIndex: 'send',
        key: 'send',
        ellipsis: true,
        render: (text, record) => {
          if (record.send) {
            return <span>{checkIcon}</span>;
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      // 9. 已送达
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.service',
          defaultValue: '已送达',
        }),
        dataIndex: 'delivery',
        key: 'delivery',
        ellipsis: true,
        render: (text, record) => {
          if (record.delivery) {
            return <span>{checkIcon}</span>;
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      // 10. 已读
      {
        title: getIntl().formatMessage({
          id: 'event.notification.status.read',
          defaultValue: '已读',
        }),
        dataIndex: 'open',
        key: 'open',
        ellipsis: true,
        render: (text, record) => {
          if (record.open) {
            return <span>{checkIcon}</span>;
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      // 11. 发送失败
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.send.failure',
          defaultValue: '发送失败',
        }),
        dataIndex: 'sendFailure',
        key: 'sendFailure',
        ellipsis: true,
        render: (text, record) => {
          if (record.sendFailure || record.error) {
            return <span>{checkIcon}</span>;
          } else {
            return (
              <span>
                <FormattedMessage
                  id="work.order.management.table.robot.work.order.no"
                  defaultMessage="否"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'marketing.results.table.operation',
          defaultValue: '操作',
        }),
        dataIndex: 'operation',
        key: 'operation',
        fixed: 'right',
        ellipsis: true,
        width: 150,
        render: (text, record) => {
          return (
            <div
              className={`${styles.operationContent}`}
              onClick={() => this.jumpToDetail(record.detailId)}
            >
              <span>
                <FormattedMessage
                  id="marketing.results.table.detail"
                  defaultMessage="详情"
                />
              </span>
            </div>
          );
        },
      },
    ];
    const ChildrenContent = () => (
      <div>
        <div className={styles.numberIcon}>{allNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="work.order.management.status.all"
            defaultMessage="全部"
          />
        </div>
      </div>
    );
    const ChildrenContent1 = () => (
      <div>
        <div className={styles.numberIcon}>{deliveryNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.service"
            defaultMessage="已送达"
          />
        </div>
      </div>
    );
    const ChildrenContent2 = () => (
      <div>
        <div className={styles.readNumberIcon}>{openNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.read"
            defaultMessage="已读"
          />
        </div>
      </div>
    );
    const ChildrenContent10 = () => (
      <div>
        <div className={styles.rejectNumIcon}>{rejectNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.reject"
            defaultMessage="拒绝"
          />
        </div>
      </div>
    );
    const ChildrenContent7 = () => (
      <div>
        <div className={styles.renderingFailureNumberIcon}>
          {renderingFailureNum}
        </div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="marketing.results.table.send.failure"
            defaultMessage="发送失败"
          />
        </div>
      </div>
    );
    const ChildrenContent8 = () => (
      <div>
        <div className={styles.sentNumberIcon}>{sentNum}</div>
        <div className={styles.tabTitle}>
          <FormattedMessage
            id="event.notification.status.have.send"
            defaultMessage="已发送"
          />
        </div>
      </div>
    );
    const tabsItems = [
      {
        key: 'all',
        label: <ChildrenContent />,
      },
      {
        key: '10',
        label: <ChildrenContent8 />,
      },
      {
        key: '1',
        label: <ChildrenContent1 />,
      },
      {
        key: '2',
        label: <ChildrenContent2 />,
      },
      {
        key: '9',
        label: <ChildrenContent7 />,
      },
    ];

    return (
      <Spin spinning={loadingDetail}>
        <div className={styles.marketingDetailsContent}>
          <div className={styles.topSelectContent}>
            <div style={{ width: '100%', height: '50px', float: 'left' }}>
              <label>
                <FormattedMessage
                  id="marketing.activities.activity.name"
                  defaultMessage="活动名称："
                />
              </label>
              <Select
                // value={activityId}
                showSearch
                allowClear={true}
                options={activityList}
                fieldNames={{
                  label: 'activityName',
                  value: 'activityId',
                  key: 'activityId',
                }}
                filterOption={(inputValue, option) =>
                  option.activityName
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.activity.name.placeholder',
                })}
                onChange={this.handleChangeActivityName}
              />
              <label>
                <FormattedMessage
                  id="marketing.results.marketing.channel.type"
                  defaultMessage="营销渠道类型："
                />
              </label>

              <Select
                showSearch
                allowClear={true}
                options={channelTypeContactList.map(item => ({
                  label: item.channelName,
                  value: item.channelId,
                  key: item.channelId,
                }))}
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.marketing.channel.type.placeholder',
                  defaultValue: '请选择营销渠道类型',
                })}
                onChange={value => {
                  this.setState({ channelType: value });
                }}
              />

              <label>
                <FormattedMessage id="marketing.results.marketing.agent.name" />
              </label>
              <Select
                allowClear={true}
                // value={eventId}
                options={eventNameList}
                showSearch
                fieldNames={{
                  label: 'eventName',
                  value: 'eventId',
                  key: 'eventId',
                }}
                filterOption={(inputValue, option) =>
                  option.eventName
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.marketing.agent.name.placeholder',
                })}
                onChange={this.handleChangeEventName}
              />
            </div>
            <div style={{ width: '100%', height: '50px', float: 'left' }}>
              <label>
                <FormattedMessage
                  id="marketing.results.marketing.event.batches"
                  defaultMessage="营销批次："
                />
              </label>
              <Select
                allowClear={true}
                // value={batchNum}
                options={eventBatchList}
                showSearch
                fieldNames={{
                  label: 'batchNum',
                  value: 'batchId',
                  key: 'batchId',
                }}
                filterOption={(inputValue, option) =>
                  option.batchNum
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
                placeholder={getIntl().formatMessage({
                  id: 'marketing.results.marketing.event.batches.placeholder',
                  defaultValue: '请输入营销事件批次',
                })}
                onChange={this.handleChangeEventBatch}
              />
              <label>
                <FormattedMessage
                  id="marketing.details.customer.phone"
                  defaultMessage="客户号码："
                />
              </label>
              <Select
                showSearch
                filterOption={(input, option) =>
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
                placeholder={getIntl().formatMessage({
                  id: 'marketing.test.phone.placeholder',
                  defaultMessage: '请输入手机号',
                })}
                value={this.state.countryCode}
                onChange={value => this.setState({ countryCode: value })}
                style={{ width: 82 }}
                options={areaNumList.map(item => ({
                  value: item.code,
                  label: item.code,
                }))}
              />
              <Input
                placeholder={getIntl().formatMessage({
                  id: 'marketing.test.phone.placeholder',
                  defaultMessage: '请输入手机号',
                })}
                value={this.state.phoneNumber}
                onChange={e => {
                  const value = e.target.value.replace(/[^\d]/g, '');
                  this.setState({ phoneNumber: value });
                }}
                maxLength={15}
              />
            </div>
            <Button
              onClick={this.handleSearch}
              type="primary"
              icon={<SearchOutlined />}
            >
              <FormattedMessage
                id="marketing.activities.search.btn"
                defaultMessage="筛选"
              />
            </Button>
          </div>
          <div className={styles.tableDetailContent}>
            <Tabs
              defaultActiveKey={workOrderStatus}
              items={tabsItems}
              onChange={key => this.changeTabOrder(key)}
            />
            <Button
              onClick={this.handleExportCustomer}
              type={'primary'}
              className={styles.exportCustomerBtn}
            >
              <FormattedMessage
                id="marketing.results.table.export.customer"
                defaultMessage="导出客户清单"
              />
            </Button>
            <Table
              dataSource={marketingDetailList}
              columns={columns}
              scroll={{
                x: 2600,
              }}
              onChange={this.onChangeStore}
              pagination={{
                total: total,
                pageSize: pageSize,
                current: pageNum,
                showSizeChanger: true,
                pageSizeOptions: [10, 20, 50, 100],
                showTotal: total => (
                  <FormattedMessage
                    id="studentManagement.altogether"
                    defaultMessage={`共 ${total} 条`}
                    values={{ total }}
                  />
                ),
              }}
            />
          </div>
        </div>
      </Spin>
    );
  }
}

const mapStateToProps = ({ statisticalResults }) => {
  return {
    ...statisticalResults,
  };
};
export default connect(mapStateToProps)(MarketingDetailsContent);
