import FormBox from '../formBox';
import CustomizePopup from '../customizePopup';
import ModalEdges from '../modalEdges';
import ModalNodes from '../modalNodes';
import ConnectionLine from '../connectionLine';
import { ReactComponent as Search } from '@/assets/Search.svg';
import { ReactComponent as AIGCEdit } from '@/assets/AIGCEdit.svg';
import { ReactComponent as DeleteHome } from '@/assets/deleteHome.svg';
import { ReactComponent as FlowLatest } from '@/assets/flow_latest.svg';
import HighLightTextarea from '../customizePopup/components/highLightTextarea';
import ReactFlow, {
  addEdge,
  Background,
  Controls,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
  MiniMap,
  MarkerType,
  getIncomers,
  getOutgoers,
  getConnectedEdges,
  applyNodeChanges,
  ControlButton,
  useReactFlow,
} from 'react-flow-renderer';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useContext,
  useImperativeHandle,
  forwardRef,
  useRef,
} from 'react';
import { useDrop } from 'react-dnd';
import { v4 as uuidv4 } from 'uuid';
import {
  FormattedMessage,
  getIntl,
  useDispatch,
  history,
  useSelector,
} from 'umi';
import { NodeStartIcon } from '../formBox/icon';
import { NodeDeteleIcon, NodeCopyIcon } from '../modalNodes/icon';
import {
  CloseOutlined,
  PlusOutlined,
  CheckOutlined,
  DownOutlined,
  TruckFilled,
} from '@ant-design/icons';
import styles from './style.less';

import {
  Button,
  notification,
  Tooltip,
  Modal,
  Input,
  Select,
  Tabs,
  Table,
  Dropdown,
  Space,
  Form,
  InputNumber,
  Typography,
  Popconfirm,
  Col,
  Row,
  Spin,
  Checkbox,
  Radio,
} from 'antd';
import {
  BackIcon,
  TipsIcon,
  EditGrayIcon,
  ForWardIcon,
  WithDrawIcon,
  VarControlsIcon,
  VarApiIcon,
  VarHorizontalIcon,
  VarVerticalIcon,
  VarSettingIcon,
  TipsSuccessIcon,
  TipsBackIcon,
  ConstIcon,
  shortcutTipsIcon,
  WhatsappTestModalCloseIcon,
  WhatsappTestModalAddIcon,
  ShareCodeIcon,
} from './icon.js';
import NewWebChatIcon from '@/assets/new-web-chat-icon.svg';
import NewAppChatIcon from '@/assets/new-app-chat-icon.svg';
import NewPhoneIcon from '@/assets/new-phone-icon.svg';
import NewWebOnlineVoiceIcon from '@/assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '@/assets/app-online-voice-icon.svg';
import NewWebOnlineVideoIcon from '@/assets/web-online-video-icon.svg';
import NewAppOnlineVideoIcon from '@/assets/app-online-video-icon.svg';
import NewWhatsAppIcon from '@/assets/new-whatsapp-icon.svg';
import NewFaceBookIcon from '@/assets/new-facebook-icon.svg';
import NewInstagramIcon from '@/assets/ins.svg';
import NewLineIcon from '@/assets/new-line-icon.svg';
import NewTwitterIcon from '@/assets/new-twitter-icon.svg';
import NewTelegramIcon from '@/assets/new-telegram-icon.svg';
import NewWeComIcon from '@/assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '@/assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '@/assets/new-wechat-official-account-icon.svg';
import NewEmailIcon from '@/assets/new-email-icon.svg';
import NewAmazonMessageIcon from '@/assets/new-amazon-message-icon.svg';
import NewShopifyIcon from '@/assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '@/assets/google-play-icon.svg';
import NewDiscordIcon from '@/assets/discord-icon.svg';
import DeleteReplyIcon from '@/assets/email-template-delete.png';
import EditorReplyIcon from '@/assets/email-template-editor.png';
import SaveReplyIcon from '@/assets/save-editor-content.png';
import CancelSaveReplyIcon from '@/assets/cancel-editor-content.png';
import ApiEmptyIcon from '@/assets/api-empty.svg';

import ShareCodeModal from '@/pages/externalIntelligentAgent/shareComponents';
import { useAsyncDispatchWithLoading } from '@/hooks/useAsyncDispatchWithLoading';
import { areaNumList } from './AreaCode';
const initialID = uuidv4();
const secondNodeID = uuidv4();
const { TextArea } = Input;

const CanvasFlow = ({ routeInfo }) => {
  const { currentVariables } = useSelector(({ aiagent }) => ({
    currentVariables: aiagent.currentVariables,
  }));
  const [shareCodeModalVisible, setShareCodeModalVisible] = useState(false);
  const tableVarRef = useRef(null);
  const reactFlowWrapper = useRef(null);
  const waitingReplyTableRef = useRef(null);
  const intelligentAgentRef = useRef(null);
  const initialNodes = [
    {
      id: initialID,
      type: 'modalNodes',
      position: {
        x: 178,
        y: 125,
      },
      data: {
        isCanvas: true,
        isStart: true,
        isHover: false, //记录鼠标悬浮状态
        isSource: false, //记录发起连接的节点，不允许自连
        inHandleProgress: false, //记录是否在连接中状态，所有节点统一状态
        componentName: getIntl().formatMessage({
          id: 'ai.agent.nodes.start',
          defaultMessage: '开始节点',
        }), //本身的名字
        nodeAlias: '', //自定义的名字
        componentColor: '#3463FC',
        nodeId: initialID,
        componentType: 'StartMarketing',
        componentToolsIcon: 'Start.svg',
        componentMiniIcon: 'MiniStart.svg',
        componentStyle: { width: 180 },
        customizForms: [
          {
            id: 'marketingStart',
            label: '',
            type: 'marketingStart',
          },
        ],
        handleList: [
          {
            type: 'source',
            position: 'right',
            id: '',
            style: {},
            target: '',
            isConnectable: true,
          },
        ],
      },
    },
  ];
  const initialEdges = [];
  const [reactFlowInstance, setReactFlowInstance] = useState(null);
  const [nodesDraggable, setNodesDraggable] = useState(true);
  const [nodes, setNodes] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [currentNodes, setCurrentNodes] = useState(null);
  const [isModalOpenChannel, setIsModalOpenChannel] = useState(false);
  const [isModalOpenBack, setIsModalOpenBack] = useState(false);
  const [isModalOpenVariables, setIsModalOpenVariables] = useState(false);
  const [isModalOpenSetting, setIsModalOpenSetting] = useState(false);
  const [isModalOpenApi, setIsModalOpenApi] = useState(false);
  const [finishJson, setFinishJson] = useState(null); //整合的最终json
  const [deployFlag, setDeployFlag] = useState(false);
  const [detailRefresh, setDetailRefresh] = useState(false);
  const [saveFlag, setSaveFlag] = useState(false);
  const [dropdownItem, setDropdownItem] = useState([]);
  const [flowLoading, setFlowLoading] = useState(false);
  const [flowLoading2, setFlowLoading2] = useState(false);
  const [loadingSave, setLoadingSave] = useState(false);
  const [loadingSaveOrDeploy, setLoadingSaveOrDeploy] = useState(false);
  const [aiAgentInfo, setAiAgentInfo] = useState(null); //接口查询的信息,永远是最新的，最正确的
  const [agentNameEditing, setAgentNameEditing] = useState(false);
  const [aiAgentNameTemp, setAiAgentNameTemp] = useState('');
  const [aiAgentId, setAiAgentId] = useState('');
  const [aiAgentStatus, setAiAgentStatus] = useState('');
  const [aiAgentData, setAiAgentData] = useState(''); //复制时路由传输的信息
  const [variableName, setVariableName] = useState('');
  const [intentId, setIntentId] = useState('');
  const [loadingBtn, setLoadingBtn] = useState(false);
  // WhatsApp营销测试相关状态
  const [whatsappTestModal, setWhatsappTestModal] = useState(false);
  const [phoneNumbers, setPhoneNumbers] = useState([
    { id: 1, countryCode: areaNumList[0].code, number: '' },
  ]);
  const {
    loading: testLoading,
    error,
    dispatchAllWithLoading,
  } = useAsyncDispatchWithLoading({
    onError: error => {
      notification.error({
        message: error.message,
      });
    },
  });
  const [intentOptions, setIntentOptions] = useState([]);
  const [currentVersion, setCurrentVersion] = useState({});
  const [componentsList, setComponentsList] = useState([]);
  const [getCurrentKeyValue, setGetCurrentKeyValue] = useState(true);
  // 设置等待回复加载
  const [loadingSetting, setLoadingSetting] = useState(false);
  const [waitSettingList, setWaitSettingList] = useState([]);

  //撤销回退操作
  const [undoStack, setUndoStack] = useState([]);
  const [redoStack, setRedoStack] = useState([]);

  //绑定节点和边的对应组件
  const nodeTypes = useMemo(() => ({ modalNodes: ModalNodes }), [setNodes]);
  const edgeTypes = useMemo(() => ({ modalEdges: ModalEdges }), []);
  // chat适用渠道类型
  const chatChannelList = [
    {
      channelName: getIntl().formatMessage({
        id: 'machineWorkloadReport.channel.web',
        defaultValue: 'WEB聊天',
      }),
      channelIcon: NewWebChatIcon,
      key: 8,
    },
    {
      channelName: getIntl().formatMessage({
        id: 'machineWorkloadReport.channel.app',
        defaultValue: 'APP聊天',
      }),
      channelIcon: NewAppChatIcon,
      key: 9,
    },
    {
      channelName: getIntl().formatMessage({
        id: 'external.intelligent.agent.create.facebook.message',
        defaultValue: 'Facebook Message',
      }),
      channelIcon: NewFaceBookIcon,
      key: 3,
    },
    {
      channelName: getIntl().formatMessage({
        id: 'marketing.channel.type.whats.app',
        defaultValue: 'WhatsApp',
      }),
      channelIcon: NewWhatsAppIcon,
      key: 4,
    },
    {
      channelName: getIntl().formatMessage({
        id: 'marketing.channel.type.instagram',
        defaultValue: 'Instagram',
      }),
      channelIcon: NewInstagramIcon,
      key: 13,
    },
    {
      channelName: getIntl().formatMessage({
        id: 'marketing.channel.type.line',
        defaultValue: 'Line',
      }),
      channelIcon: NewLineIcon,
      key: 14,
    },
    {
      channelName: getIntl().formatMessage({
        id: 'marketing.channel.type.weCom',
        defaultValue: '微信客服',
      }),
      channelIcon: NewWeComIcon,
      key: 15,
    },
    {
      channelName: getIntl().formatMessage({
        id: 'marketing.channel.type.weChat.mini.program',
        defaultValue: '微信小程序',
      }),
      channelIcon: NewWeChatMiniProgramIcon,
      key: 21,
    },
    {
      channelName: getIntl().formatMessage({
        id: 'marketing.channel.type.weChat.official.account',
        defaultValue: '微信公众号',
      }),
      channelIcon: NewWechatOfficialAccountIcon,
      key: 16,
    },
    // {
    //   channelName: getIntl().formatMessage({
    //     id: 'marketing.channel.type.amazon.message',
    //     defaultValue: 'Amazon Message',
    //   }),
    //   channelIcon: NewAmazonMessageIcon,
    //   key: 12,
    // },
    {
      channelName: getIntl().formatMessage({
        id: 'marketing.channel.type.shopify',
        defaultValue: 'Shopify',
      }),
      channelIcon: NewShopifyIcon,
      key: 22,
    },
  ];
  // 电话适用渠道
  const phoneChannelList = [
    {
      channelName: getIntl().formatMessage({
        id: 'marketing.channel.type.phone',
        defaultValue: '电话',
      }),
      channelIcon: NewPhoneIcon,
      key: 7,
    },
    {
      channelName: getIntl().formatMessage({
        id: 'marketing.channel.type.web.voice',
        defaultValue: 'WEB语音',
      }),
      channelIcon: NewWebOnlineVoiceIcon,
      key: 17,
    },
    {
      channelName: getIntl().formatMessage({
        id: 'marketing.channel.type.app.voice',
        defaultValue: 'APP语音',
      }),
      channelIcon: NewAppOnlineVoiceIcon,
      key: 18,
    },
  ];

  // 放下拖拽内容，传输node节点内容，作为formNode和modalNodes之间的数据传输
  const [_, drop] = useDrop(
    () => ({
      accept: 'node',
      drop: (item, monitor) => {
        const offset = monitor.getClientOffset();
        const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
        const position = reactFlowInstance
          ? reactFlowInstance.project({
              x: offset?.x - reactFlowBounds.left ?? 0,
              y: offset?.y - reactFlowBounds.top ?? 0,
            })
          : {
              x: offset?.x - reactFlowBounds.left ?? 0,
              y: offset?.y - reactFlowBounds.top ?? 0,
            };
        const newItem = { ...item };
        switch (newItem.componentType) {
          case 'MessageText':
            newItem.customizForms = [
              {
                id: 'MessageText',
                label: '',
                type: 'span',
              },
            ];
            newItem.handleList = [
              {
                type: 'source',
                position: 'right',
                id: '',
                style: {},
                target: '',
                isConnectable: true,
              },
            ];
            break;
          case 'MessageRichText':
            newItem.customizForms = [
              {
                id: 'MessageRichText',
                label: '',
                type: 'markdown',
              },
            ];
            newItem.handleList = [
              {
                type: 'source',
                position: 'right',
                id: '',
                style: {},
                target: '',
                isConnectable: true,
              },
            ];
            break;
          case 'MessageImage':
            newItem.customizForms = [
              {
                id: 'MessageImage',
                label: '',
                url: '',
                showUrl: '',
                type: 'img',
                radioValue: '1',
                handle: 'MessageImage',
                index: 1,
              },
              {
                id: 'Fallback',
                label: '',
                handle: 'Fallback',
                type: 'span',
                index: 2,
              },
            ];
            newItem.isRequire = {
              url: getIntl().formatMessage(
                { id: 'ai.agent.verification.node' },
                {
                  nodeName: newItem.nodeAlias
                    ? newItem.nodeAlias
                    : newItem.componentName,
                  formName: 'url',
                },
              ),
            };
            newItem.handleList = [];
            break;
          case 'MessageMedia':
            newItem.customizForms = [
              {
                id: 'MessageMedia',
                label: '',
                url: '',
                showUrl: '',
                type: 'video',
                radioValue: '1',
                handle: 'MessageMedia',
                index: 1,
              },
              {
                id: 'Fallback',
                label: '',
                handle: 'Fallback',
                type: 'span',
                index: 2,
              },
            ];
            newItem.isRequire = {
              url: getIntl().formatMessage(
                { id: 'ai.agent.verification.node' },
                {
                  nodeName: newItem.nodeAlias
                    ? newItem.nodeAlias
                    : newItem.componentName,
                  formName: 'url',
                },
              ),
            };
            newItem.handleList = [];
            break;
          case 'MessageDoc':
            newItem.customizForms = [
              {
                id: 'MessageDoc',
                label: '',
                url: '',
                showUrl: '',
                type: 'component',
                radioValue: '1',
                handle: 'MessageDoc',
                index: 1,
                fileName: '',
              },
              {
                id: 'Fallback',
                label: '',
                handle: 'Fallback',
                type: 'span',
                index: 2,
              },
            ];
            newItem.isRequire = {
              url: getIntl().formatMessage(
                { id: 'ai.agent.verification.node' },
                {
                  nodeName: newItem.nodeAlias
                    ? newItem.nodeAlias
                    : newItem.componentName,
                  formName: 'url',
                },
              ),
            };
            newItem.handleList = [];
            break;
          case 'ToolToAgent':
            newItem.customizForms = [
              {
                id: 'ToolToAgent',
                label: getIntl().formatMessage({
                  id: 'ai.agent.nodes.ToolToAgent.input.default',
                }),
                judgmentRuleType: 1, // 分配规则    1-系统自动分配   2-人工指定
                distributionRuleType: 1, //  1-分配给特定团队；2-分配给特定坐席
                distributionIds: [], // 分配指定的坐席或者团队 ids  逗号拼接
                workRecordTypeCode: [], //  工单类型code
                priorityLevelId: '', // 优先级id
                customerLabel: '', // 客户标签ID，以逗号分隔,
                intent: '', // 进线意图
                type: 'form',
                handle: 'ToolToAgent',
                index: 1,
              },
              {
                id: 'Fallback',
                label: '',
                handle: 'Fallback',
                type: 'span',
                index: 2,
              },
            ];
            newItem.handleList = [];
            break;
          case 'ToolLLM':
            newItem.customizForms = [
              {
                id: 'ToolLLM',
                prompt: '', // 提示词
                isAIGC: 1, // 是否AIGC （0否，1是）
                content: '', //  AIGC
                resTyep: '', // 返回类型（暂时只有文本）
                variablesKey: [], // 存储变量
                type: 'form',
                handle: 'ToolLLM',
                index: 1,
                contentShow: '1',
              },
              {
                id: 'Fallback',
                label: '',
                handle: 'Fallback',
                type: 'span',
                index: 2,
              },
            ];
            newItem.handleList = [];
            break;
          case 'ToolFailure':
            newItem.customizForms = [{ id: 'ToolFailure' }];
            newItem.handleList = [];
            break;
          case 'AskQuestionText':
            newItem.customizForms = [
              // 翔总要的格式最好统一
              {
                id: 'AskQuestionText', //id handle是按钮
                handle: 'AskQuestionText', //唯一名称不重复
                index: 1,
                type: 'form',
                content: '', // 机器人话术
                label: '', //回显用
                formatType: 0, // 格式类型（0无需校验 1时间，3数值，4正则表达式）
                regType: 0, // 校验类型（0无需校验 1时间，3数值，4正则表达式）
                regType1: 'None',
                llmRulePrompt: '', // LLM校验
                timeType: '', // 时间范围，定义好的规则（1自定义 2今天之前 3今天之后）
                timeZone: '', // 时区
                telephonePrefix: '', //回显时区
                startTime: '', // 时间范围，格式类型为date时存在，自定义
                endTime: '', // 时间范围，格式类型为date时存在，自定义
                maxNum: '', // 最大数值
                minNum: '', // 最小数值
                regexPattern: '', // 正则表达式
                retryCount: '', // 校验失败重试次数
                failureResponse: '', // 校验失败后的回复
                finalFailureResponse: '', //最终校验失败后的回复
                storedValue: '', //存储到变量
                vMethod: 0, // 校验方式
              },
              {
                id: 'Fallback',
                label: '',
                content: '',
                handle: 'AskQuestionTextFallBack',
                index: 2,
                type: 'form',
                content: '', // 机器人话术
                verifyFormat: 1, // 是否验证格式（1验证，0不验证）
                formatType: '', // 格式类型（1时间，2邮箱，3数值，4正则表达式）
                dateTyoe: '', // 时间格式，格式类型为 1 时存在
                timeType: '', // 时间范围，定义好的规则
                timeZone: '', // 时区
                startTime: '', // 时间范围，格式类型为date时存在，自定义
                endTime: '', // 时间范围，格式类型为date时存在，自定义
                regexPattern: '', // 正则表达试
                retryCount: '', // 校验失败重试次数
                failureResponse: '', // 校验失败后的回复
                finalFailureResponse: '', //最终校验失败后的回复
                storedValueType: '', // 存储变量类型
                storedValue: '', //存储到变量key
              },
            ];
            newItem.handleList = [];
            break;
          case 'AskQuestionButton':
            newItem.customizForms = [
              {
                id: 'MessageText', //id handle是按钮
                content: '', // 机器人话术
                label: '',
                displayType: '0', // 按钮排列方式
                saveSelectedToAttr: '', // 存储到变量
              },
              {
                id: 'handle', //id handle是按钮
                handle: 'AskQuestionButton1', //唯一名称不重复
                index: 1,
                content: '', // 机器人话术
                label: '', //回显用
                buttonName: '', // 按钮名称
                buttonValue: '', // 按钮值
                buttonId: '', // 按钮id
                nextNodeId: '', // 下一个节点
              },
              {
                id: 'Fallback',
                label: '',
                content: '',
                handle: 'AskQuestionTextFallBack',
                index: 2,
                content: '', // 机器人话术
              },
            ];
            newItem.handleList = [];
            // newItem.handleList = [
            //   {
            //     type: 'source',
            //     position: 'right',
            //     id: 'AskQuestionTextFallBack',
            //     style: {
            //       top: '100%',
            //       backgroundColor: '#F22417',
            //     },
            //   },
            // ];
            break;
          case 'AskQuestionForm':
            newItem.customizForms = [
              {
                id: 'Form', //id handle是按钮
                handle: 'AskQuestionForm', //唯一名称不重复
                attributeName: '', // 属性名称
                attributeType: '1', // 属性类型
                attributeValues: [], // 属性值
                formName: '', // 显示名称 formName
                inputTip: '', // 属性输入提示
                required: 0, // 是否必填（0，非，1是）
                regexType: 'None', // 验证格式
                regexPattern: '', // 正则表达式
                variablesKey: '', // 存储变量
                isOpen: true,
              },
              {
                id: 'handle', //id handle是按钮
                handle: 'AskQuestionFormButton1', //唯一名称不重复
                index: 1,
                content: getIntl().formatMessage({
                  id: 'ai.agent.nodes.AskQuestionForm.addForm.button1',
                }), // 机器人话术
                label: getIntl().formatMessage({
                  id: 'ai.agent.nodes.AskQuestionForm.addForm.button1',
                }), //回显用
              },
              {
                id: 'handle', //id handle是按钮
                handle: 'AskQuestionFormButton2', //唯一名称不重复
                index: 2,
                content: getIntl().formatMessage({
                  id: 'ai.agent.nodes.AskQuestionForm.addForm.button2',
                }), // 机器人话术
                label: getIntl().formatMessage({
                  id: 'ai.agent.nodes.AskQuestionForm.addForm.button2',
                }), //回显用
              },
              {
                id: 'Fallback',
                label: '',
                content: '',
                handle: 'AskQuestionTextFallBack',
                index: 3,
                content: '', // 机器人话术
              },
            ];
            newItem.handleList = [];
            break;
          case 'AskQuestionLLMInfoCollection':
            newItem.customizForms = [
              {
                id: 'AskQuestionText', //id handle是按钮
                handle: 'AskQuestionLLM', //唯一名称不重复
                content: '', //机器人话术
                index: 1,
              },
              {
                id: 'AskQuestionLLM',
                handle: 'AskQuestionLLM',
                attributeName: '', // 属性名称
                attributeCode: '', // 属性编码
                attributeFormat: '', // 属性格式要求，非必填
                required: 0, // 是否必填（0，非，1是）
                variablesKey: '', // 存储变量
                isOpen: true,
              },
              {
                id: 'Fallback',
                label: '',
                content: '',
                handle: 'AskQuestionTextFallBack',
                index: 2,
                content: '', // 机器人话术
              },
            ];
            newItem.handleList = [];
            break;
          //热点问题
          case 'MessageHotIssue':
            newItem.customizForms = [
              {
                index: 1,
                id: 'MessageHotIssue',
                handle: 'MessageHotIssue',
                configType: '1', // 1 手动；2 自动
                showType: '1', // 1 横；2 竖
                originLanguage: '', //原始语言
                issues: [], //问题列表
                recentDay: '', //最近x天
                topFAQ: '', // 最多的Top X个 FAQ
              },
            ];
            newItem.handleList = [];
            break;
          //动态卡片
          case 'AskQuestionCard':
            newItem.customizForms = [
              {
                index: 1,
                id: 'AskQuestionCard',
                handle: 'AskQuestionCard',
                cardLayout: '1', // 1 轮播，2 列表
                cardID: '',
                cardListAttr: '',
                cardImageUrl: '',
                cardTitle: '',
                cardPrice: '',
                cardNumber: '',
                cardStatus: '',
                saveSelectedToAttr: '',
                nextNodeId: '', // 下一个节点
              },
              {
                id: 'Fallback',
                label: '',
                content: '',
                handle: 'AskQuestionTextFallBack',
                index: 2,
                content: '', // 机器人话术
              },
            ];
            newItem.handleList = [];
            break;
          case 'ToolDelay':
            newItem.customizForms = [
              {
                index: 1,
                id: 'ToolDelay', //id handle是按钮
                handle: 'ToolDelay', //唯一名称不重复
                delayTime: '0', // 延时时间
                nextNodeId: '', // 下一个节点
              },
            ];
            newItem.handleList = [];
            break;
          case 'ConditionIntent': //意图识别
            newItem.customizForms = [
              {
                id: 'handle', //id handle是按钮
                handle: 'ConditionIntent1', //唯一名称不重复
                index: 1,
                content: '', // 机器人话术
                label: '', //回显用
                intentName: '', // 意图名称
                intentJudge: '', // 意图判断依据
                nextNodeId: '', // 下一个节点
              },
              // {
              //   id: 'Fallback',
              //   label: '',
              //   content: '',
              //   handle: 'ConditionIntentFallBack',
              //   index: 2,
              //   content: '', // 机器人话术
              // },
            ];
            newItem.handleList = [
              {
                type: 'source',
                position: 'right',
                id: 'Fallback',
                style: {
                  top: '100%',
                  backgroundColor: '#F22417',
                },
              },
            ];
            break;
          case 'ToolRag': //知识库
            newItem.customizForms = [
              {
                id: 'Knowlege', //id handle是按钮
                handle: 'ToolRag', //唯一名称不重复
                index: 1,
                content: '', // 机器人话术
                label: '', //回显用
                restQuestion: '', // 重写用户问题
                useFaq: 0, // FAQ 知识库（勾选为1 ，不勾选为0）
                useQa: 0, // RAG 知识库（勾选为1 ，不勾选为0）
                knowledgeId: '', // 知识库ID
                knowledgeName: '', // 知识库ID
                moodBoardFlag: '', // 问答风格 0 专业 1温柔
                lackKnowledge: '', // 缺乏知识回答
                nextNodeId: '', // 下一个节点
                tags: [],
                tagList: [],
              },
              {
                id: 'Warning',
                label: '',
                content: '',
                handle: 'ToolRagWarning',
                index: 2,
                content: '', // 机器人话术
              },
              {
                id: 'Fallback',
                label: '',
                content: '',
                handle: 'ToolRagFallBack',
                index: 2,
                content: '', // 机器人话术
              },
            ];
            newItem.handleList = [];
            break;
          case 'ToolAPI': //API
            newItem.customizForms = [
              {
                id: 'ToolAPI', //id handle是按钮
                handle: 'ToolAPI', //唯一名称不重复
                index: 1,
                apiId: '', // 选择API的ID
                apiName: '',
                variablesKey: '', // 存储变量
                nextNodeId: '', // 下一个节点
                type: 'span',
              },
              {
                id: 'Fallback',
                label: '',
                content: '',
                handle: 'ToolAPIFallBack',
                index: 2,
                content: '', // 机器人话术
                type: 'span',
              },
            ];
            newItem.handleList = [];
            break;
          case 'ToolWorkHours':
            newItem.customizForms = [
              {
                id: 'handle', //id handle是按钮
                handle: 'ToolWorkHours', //唯一名称不重复
                index: 1,
                label: getIntl().formatMessage({
                  id: 'ai.agent.nodes.ToolWorkHours.workTime',
                }),
                nextNodeId: '', // 下一个节点
                workTimeId: '', //选择的工作时间
              },
              {
                id: 'handle', //id handle是按钮
                handle: 'ToolWorkHoursNot', //唯一名称不重复
                index: 2,
                label: getIntl().formatMessage({
                  id: 'ai.agent.nodes.ToolWorkHours.notWorkTime',
                }),
                nextNodeId: '', // 下一个节点
                type: 'span',
              },
              {
                id: 'Fallback',
                handle: 'ToolWorkHoursFallback',
                index: 3,
              },
            ];
            newItem.handleList = [];
            break;
          case 'ToolVariableSetting': //
            newItem.customizForms = [
              {
                id: 'ToolVariableSetting', //id handle是按钮
                handle: 'ToolVariableSetting', //唯一名称不重复
                index: 1,
                dateType: '', //变量类型
                variableName: '', //变量名称
                adjustRule: '', //调整规则类。 不同类型---求和求差。。/前缀后缀
                params: [], //存放与调整规则相关的参数。两个参数
              },
              {
                id: 'Fallback',
                handle: 'ToolVariableSettingFallback',
                index: 2,
              },
            ];
            newItem.handleList = [];
            break;
          case 'ToolUpdateCustomer': //
            newItem.customizForms = [
              {
                id: 'ToolUpdateCustomer', //id handle是按钮
                handle: 'ToolUpdateCustomer1', //唯一名称不重复
                index: 1,
                attributeName: '',
                attributeValue: '', // 属性值
              },
              {
                id: 'Fallback',
                handle: 'ToolUpdateCustomerFallback',
                index: 2,
              },
            ];
            newItem.handleList = [];
            break;
          case 'ToolUpdateTicket': //
            newItem.customizForms = [
              {
                id: 'ToolUpdateTicket', //id handle是按钮
                handle: 'ToolUpdateTicket1', //唯一名称不重复
                index: 1,
                attributeName: '',
                attributeValue: '', // 属性值
              },
              {
                id: 'Fallback',
                handle: 'ToolUpdateTicketFallback',
                index: 2,
              },
            ];
            newItem.handleList = [];
            break;
          case 'ConditionCheck': //
            newItem.customizForms = [
              {
                id: 'ConditionCheck', //id handle是按钮
                handle: 'ConditionCheck1', //唯一名称不重复
                index: 1,
                inputType: '', // 字符串类型，选择的条件类型，0客户输入(表示聊天是用户最后一次输入)，1变量​
                conditionType: '', // 字符串类型，判读条件类型，0等于，1包含，2为空，3不为空​
                inputValue: '', // 字符串类型，输入值，可以是文本也可以选择变量
                nextNodeId: '', // 字符串类型，下一个节点d
              },
              {
                id: 'ConditionCheckElse', //id handle是按钮
                handle: 'ConditionCheckElse', //唯一名称不重复
                index: 2,
                nextNodeId: '', // 字符串类型，下一个节点d
              },
              {
                id: 'Fallback',
                handle: 'ConditionCheckFallback',
                index: 3,
              },
            ];
            newItem.handleList = [];
            break;
          case 'ToolSetCustomerTag':
            newItem.customizForms = [
              {
                id: 'ToolSetCustomerTag', //id handle是按钮
                handle: 'ToolSetCustomerTag', //唯一名称不重复
                index: 1,
                tagList: [],
                tagName: [],
                nextNodeId: '',
              },
              {
                id: 'Fallback',
                handle: 'ToolSetCustomerTagFallback',
                index: 2,
              },
            ];
            newItem.handleList = [];
            break;
          case 'MarketingMessage':
            newItem.customizForms = [
              {
                id: 'MarketingMessage',
                label: '',
                type: 'MarketingMessage',
              },
            ];
            newItem.handleList = [
              {
                type: 'source',
                position: 'right',
                id: '',
                style: {},
                target: '',
                isConnectable: true,
              },
            ];
            break;
        }
        setNodes(nds => [
          ...nds,
          {
            id: newItem.nodeId,
            type: 'modalNodes',
            position,
            data: {
              isCanvas: true,
              isStart: false,
              ...newItem,
              setNodesDraggable: setNodesDraggable,
              nodesDraggable: nodesDraggable,
            },
          },
        ]);
      },
    }),
    [reactFlowInstance],
  );
  const dispatch = useDispatch();

  // 连接事件，type的值对应边组件
  const onConnect = params => {
    let nodeSource = null;
    let nodeTarget = null;
    let markerEnd = {
      type: MarkerType.ArrowClosed,
      color: '#3463FC',
    };
    let style = {};
    nodes.forEach(item => {
      if (item.id === params?.source) {
        nodeSource = item;
      }
      if (item.id === params?.target) {
        nodeTarget = item;
      }
    });

    console.log(nodes, params, nodeTarget, nodeSource, 'onConnect');
    //连接校验
    if (!connectVerification(nodeTarget, nodeSource)) {
      return;
    }
    //设置边的颜色
    if (params?.source && nodeSource?.data?.handleList) {
      nodeSource.data.handleList?.forEach(item => {
        if (item.id === params.sourceHandle) {
          let c = item.style.backgroundColor ?? '#3463FC';
          markerEnd.color = c;
          style = { stroke: c };
        }
      });
    }
    setEdges(eds => {
      const edge = { ...params, type: 'modalEdges', markerEnd, style };
      return addEdge(edge, eds);
    });
    //以下逻辑用于限制一个句柄仅可以产出一条连接线
    setNodes(nds => {
      let newNds = nds?.map((item, i) => {
        let it = item;
        if (it.id === params?.source) {
          it.data.handleList?.forEach(handle => {
            // console.log('onConnect1', it);
            // console.log(handle.id, params?.sourceHandle, 'onConnect1');
            if (
              handle.id === params?.sourceHandle ||
              (!params?.sourceHandle && !handle.id)
            ) {
              handle.isConnectable = false;
            }
          });
        }
        return it;
      });
      return newNds;
    });
  };
  //所有节点与节点之间连接时的校验
  const connectVerification = (nodeTarget, nodeSource) => {
    let result = true;
    //意图识别组件，只能关联在提问问题中的文字组件后边
    // if (
    //   nodeTarget?.data?.componentType === 'ConditionIntent' &&
    //   nodeSource?.data?.componentType !== 'AskQuestionText'
    // ) {
    //   result = false;
    //   notification.error({
    //     message: getIntl().formatMessage({
    //       id: 'ai.agent.verification.conditionIntent',
    //     }),
    //   });
    // }
    return result;
  };
  //删除节点操作
  const onNodesDelete = deleted => {
    setEdges(
      deleted.reduce((acc, node) => {
        const incomers = getIncomers(node, nodes, edges);
        const outgoers = getOutgoers(node, nodes, edges);
        const connectedEdges = getConnectedEdges([node], edges);

        const remainingEdges = acc.filter(
          edge =>
            !connectedEdges.includes(edge) || node.componentType === 'Start',
        );

        const createdEdges = incomers.flatMap(({ id: source }) =>
          outgoers.map(({ id: target }) => ({
            id: `${source}->${target}`,
            source,
            target,
          })),
        );
        return [...remainingEdges, ...createdEdges];
      }, edges),
    );
    setNodes(nds => {
      if (deleted?.[0]?.data?.componentType === 'Start') {
        return [...nds];
      }
    });
  };
  //删除节点
  const deleteNode = () => {
    if (currentNodes && currentNodes.id && !currentNodes.data.isStart) {
      let nodeName = '';
      let nodeId = '';
      //以下逻辑用于限制一个句柄仅可以产出一条连接线
      setEdges(edges =>
        edges.filter(edge => {
          if (edge.target !== currentNodes.id) {
            return edge;
          } else {
            nodeId = edge.source;
            nodeName = edge.sourceHandle;
          }
        }),
      );

      setNodes(nds => {
        let newNds = nds.filter((item, i) => {
          if (item.id === nodeId) {
            item.data.handleList?.forEach(handle => {
              if (handle.id === nodeName || (!nodeName && !handle.id)) {
                handle.isConnectable = true;
              }
            });
            return item;
          } else if (item.id !== currentNodes.id) {
            return item;
          }
        });
        return newNds;
      });
    } else {
      notification.error({
        message: getIntl().formatMessage({
          id: 'ai.agent.nodes.start.illegal',
        }),
      });
    }
  };
  //复制节点
  const copyNode = () => {
    if (currentNodes && currentNodes.id && !currentNodes.data.isStart) {
      setNodes(nds => {
        let newNds = nds.filter((item, i) => item.id === currentNodes.id)[0];
        let x = newNds.position.x;
        let y = newNds.position.y;
        let uuid = uuidv4();
        let newHandleList = newNds.data.handleList?.map(handle => {
          return { ...handle, isConnectable: true };
        });
        return [
          ...nds,
          {
            ...newNds,
            id: uuid,
            position: { x: x + 50, y: y + 50 },
            selected: false,
            data: {
              ...newNds.data,
              nodeId: uuid,
              customizForms: [...newNds.data.customizForms],
              handleList: newHandleList,
            },
          },
        ];
      });
    } else {
      notification.error({
        message: getIntl().formatMessage({
          id: 'ai.agent.nodes.start.illegal',
        }),
      });
    }
  };

  //单独处理节点改变逻辑
  const onNodesChange = useCallback(
    changes => {
      setEdges(eds => {
        console.log(eds, changes, 'onNodesChange===这是eds');
        // 检查changes中是否有删除节点的操作
        const removeChanges = changes.filter(change => change.type === 'reset');
        let newEds = [...eds];
        // 检查sourceHandle是否存在于任何被删除节点的customizForms中的handle属性
        if (removeChanges.length > 0) {
          eds.forEach((edge, index) => {
            let handleExists = false;
            if (edge.sourceHandle) {
              // 遍历所有被删除的节点
              for (const node of removeChanges) {
                if (node?.item?.data?.customizForms) {
                  let currentCustomizForms = node.item.data.customizForms;
                  // 检查该节点的customizForms中是否有匹配的handle
                  handleExists = currentCustomizForms.some(
                    form => form.handle === edge.sourceHandle,
                  );
                  if (handleExists) {
                    break;
                  }
                }
              }
            } else {
              handleExists = true;
            }
            if (!handleExists) {
              newEds.splice(index, 1);
            }
          });
        }
        return [...newEds];
      });
      setNodes(nds => {
        console.log(changes, nds, 'onNodesChange===');
        let flag = false;
        changes?.forEach(item => {
          // 拦截对开始节点的操作
          if (item?.type === 'remove' && item.id === nds?.[0]?.id) {
            flag = true;
          }
        });
        if (flag) {
          // //不可删除开始节点，返回原数组
          notification.error({
            message: getIntl().formatMessage({
              id: 'ai.agent.nodes.start.noDelete',
              defaultValue: '开始节点不允许删除！',
            }),
          });
          return [...nds];
        } else {
          return applyNodeChanges(changes, nds);
        }
      });
    },
    [setNodes],
  );
  const onSelectionChange = useCallback(
    select => {
      console.log(select.nodes, 'currentNodes');
      if (select.nodes.length === 0) {
        setCurrentNodes(null);
        setNodes(nds => {
          let newNds = nds?.map((item, i) => {
            let it = item;
            it.data.isHover = false;
            it.data.isEdit = false;
            it.data.inHandleProgress = false;
            it.data.isSource = false;

            return it;
          });
          return newNds;
        });
      } else {
        setCurrentNodes(select.nodes?.[0]);
      }
    },
    [setNodes],
  );
  const onConnectStart = value => {
    console.log('长按事件触发！', value);
    setNodes(nds => {
      let newNds = nds?.map((item, i) => {
        let it = item;
        it.data.inHandleProgress = true;
        if (item.id === value.nodeId) {
          it.data.isSource = true;
        }
        return it;
      });
      return newNds;
    });
  };
  const onConnectStop = () => {
    setNodes(nds => {
      let newNds = nds?.map((item, i) => {
        let it = item;
        it.data.inHandleProgress = false;
        it.data.isSource = false;
        return it;
      });
      return newNds;
    });
  };
  const onNodeMouseEnter = value => {
    setNodes(nds => {
      let newNds = nds?.map((item, i) => {
        let it = item;
        if (item.id === value.id) {
          it.data.isHover = true;
        }
        return it;
      });
      return newNds;
    });
    console.log('鼠标事件进入！', value);
  };
  const onNodeMouseMove = value => {
    console.log('鼠标事件移动！', value);
  };
  const onNodeMouseLeave = value => {
    setNodes(nds => {
      let newNds = nds?.map((item, i) => {
        let it = item;
        if (item.id === value.id) {
          it.data.isHover = false;
        }
        return it;
      });
      return newNds;
    });
    console.log('鼠标事件离开！', value);
  };
  //编辑节点内容
  const onNodeClick = () => {};

  /**************************************************接口和自定义函数分界线，上面全是插件的函数，下面是自定义的接口及函数*****************************************/
  //复制智能体，替换主键id
  const copyAiAgent = () => {
    console.log('copy:aiAgentData?.aiAgentInfo', aiAgentData?.aiAgentInfo);
    if (!aiAgentData?.aiAgentInfo) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'ai.agent.copy.fail',
        }),
      });
      goBack();
      return false;
    } else {
      let nodesList = aiAgentData?.aiAgentInfo?.nodesList;
      let edgeList = aiAgentData?.aiAgentInfo?.edgeList;
      //名字赋值
      setAiAgentNameTemp(aiAgentData.aiAgentName);
      setAgentNameEditing(false);
      setAiAgentInfo(aiAgentData);
      let ids = nodesList?.map(item => {
        return item.id;
      });
      console.log(
        aiAgentData,
        ids,
        nodesList,
        aiAgentData,
        '复制体aiAgentData',
      );
      replaceID(nodesList, edgeList, ids, 0);
    }
  };
  const replaceID = (nl, el, ids, index) => {
    if (index >= ids?.length) {
      setNodes(nl);
      setEdges(el);
    } else {
      let id = ids[index];
      let uuid = uuidv4();
      let regex = new RegExp(id, 'g');
      let jsonStringNl = JSON.stringify(nl);
      let jsonStringEl = JSON.stringify(el);
      let newJsonStringNl = JSON.parse(jsonStringNl.replace(regex, uuid));
      let newJsonStringEl = JSON.parse(jsonStringEl.replace(regex, uuid));
      replaceID(newJsonStringNl, newJsonStringEl, ids, index + 1);
    }

    // console.log(
    //   uuid,
    //   id,
    //   jsonStringNl,
    //   newJsonStringNl,
    //   jsonStringEl,
    //   newJsonStringEl,
    //   'jsonStringEl',
    // );
  };
  //复制之后新建智能体信息
  const createNewAiAgentInfo = newNodes => {
    let params = {
      aiAgentId: aiAgentId, //id
      aiAgentName: aiAgentInfo.aiAgentName, //智能体名称，名字需要重新赋值，取aiAgentInfo的
      aiAgentType: aiAgentData.aiAgentType, //智能体类型，1:正常，2:欢迎语，3:fallback
      companyId: aiAgentData.companyId, //公司id
      channelTypeId: aiAgentInfo.channelTypeId, //1:聊天，2:邮件，3:电话
      channelIds: aiAgentInfo.channelIds, //来源渠道，和咱们定义的那个序号1-20+对应的id,多个逗号分隔
      triggerType: aiAgentData.triggerType, //1:意图 2: 其他智能体 3:没有
      intentionId: newNodes.intentId, //意图id
      // intentName: newNodes.intentName, //意图名称
    };
    setFlowLoading(true);
    dispatch({
      type: 'intentionManagement/saveAgentInfo',
      payload: params,
      callback: response => {
        setFlowLoading(false);
        if (response.code === 200) {
          notification.success({
            message: getIntl().formatMessage({
              id: 'customer.ext.info.save.success',
            }),
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询智能体名称渠道等信息
  const getAiAgentDetail = options => {
    setFlowLoading(true);
    dispatch({
      type: 'aiagent/getAiAgentDetail',
      payload: {
        aiAgentId: aiAgentId,
      },
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          setAiAgentInfo(data);
          setAiAgentNameTemp(data.aiAgentName);
          if (data.aiAgentInfo && JSON.stringify(data.aiAgentInfo) !== '{}') {
            console.log(
              'data.aiAgentInfo.nodesList',
              data.aiAgentInfo.nodesList,
            );
            let newNodes = data.aiAgentInfo.nodesList.map(item => {
              item.data.setNodesDraggable = setNodesDraggable;
              item.data.nodesDraggable = nodesDraggable;
              return item;
            });
            setNodes(newNodes);
            setEdges(data.aiAgentInfo.edgeList);
          } else if (data.intentionId && data.intentName) {
            // intentChange(data.intentionId, options); // 无意图
          }
          setAgentNameEditing(false);
          setDetailRefresh(true);
        } else {
          notification.error({
            message: msg,
          });
        }
        setFlowLoading(false);
      },
    });
  };
  //初始化意图
  const intentChange = (e, options) => {
    let arr = {};
    options?.forEach(item => {
      item[1]?.forEach(it => {
        if (it.intentId === e) {
          arr = it;
        }
      });
    });
    console.log('这里是arr', arr, options, e);
    setNodes(nds => {
      let newNds = nds?.map((item, i) => {
        let it = item;
        if (it.data.componentType === 'Start') {
          console.log('这里是node', it.data);
          it.data.customizForms[0].currentIntentInfo = arr;
          it.data.customizForms[0].label = arr?.intentName;
          it.data.customizForms[0].children = arr?.scriptList?.map(
            (it, index) => {
              return {
                id: 'li',
                label: it.scriptName,
                type: 'li',
                scriptId: it.scriptId,
              };
            },
          );
          it.data.customizForms[0].attributesList = arr?.attributesList?.map(
            (it, index) => {
              return {
                attributeName: it.attributeName,
                attributeId: it.attributeId,
              };
            },
          );
          //暂时没有动态变量
          it.data.customizForms[0].variableList = '';
        }
        console.log('这里是it', it);
        return it;
      });
      console.log('这里是newNds', newNds);
      return newNds;
    });
  };
  //查意图
  const getIntentGrouped = () => {
    dispatch({
      type: 'aiagent/getIntentGrouped',
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          let newData = Object.entries(data);
          setIntentOptions(newData);
          getAiAgentDetail(newData);
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  //整合JSON
  const getFinishJson = () => {
    //整合nodes和edges，边和节点
    let newNodes = {};
    let newIntentId = '';
    newNodes.nodesList = [...nodes];
    newNodes.edgeList = [...edges];
    // //整合后端需要的parameters数据
    newNodes.nodesList?.forEach(item => {
      item.data.parameters = [];
      newNodes.edgeList?.forEach(edgeItem => {
        //多连接点情况赋值parameters
        if (edgeItem.sourceHandle && edgeItem.source) {
          item.data.customizForms?.forEach(itemSon => {
            if (
              edgeItem.sourceHandle === itemSon.handle &&
              edgeItem.source === item.id
            ) {
              console.log('zhenghe>>>mul', itemSon);
              if (
                itemSon.id === 'ToolToAgent' &&
                itemSon.handle != 'AskQuestionText'
              ) {
                item.data.parameters.push({
                  content: item.data.customizForms?.[0].label, // 默认消息
                  judgmentRuleType:
                    item.data.customizForms?.[0].judgmentRuleType, // 分配规则    1-系统自动分配   2-人工指定
                  distributionRuleType:
                    item.data.customizForms?.[0].distributionRuleType, //  1-分配给特定团队；2-分配给特定坐席
                  distributionIds: item.data.customizForms?.[0].distributionIds?.join(
                    ',',
                  ), // 分配指定的坐席或者团队 ids  逗号拼接
                  workRecordTypeCode:
                    item.data.customizForms?.[0].workRecordTypeCode, //  工单类型code
                  priorityLevelId: item.data.customizForms?.[0].priorityLevelId, // 优先级id
                  customerLabel: item.data.customizForms?.[0].customerLabel, // 客户标签ID，以逗号分隔,
                  intent: item.data.customizForms?.[0].intent, // 进线意图
                  nextNodeId: edgeItem.target,
                });
              } else if (itemSon.id == 'ToolSetCustomerTag') {
                item.data.parameters = [
                  {
                    tagList: item.data.customizForms?.[0].tagList,
                    nextNodeId: edgeItem.target,
                  },
                ];
              } else if (
                itemSon.handle == 'ToolAPI' &&
                itemSon.id == 'ToolAPI'
              ) {
                item.data.parameters = [
                  {
                    apiId: itemSon.apiId,
                    variablesKey: itemSon.variablesKey,
                    nextNodeId: edgeItem.target,
                  },
                ];
              } else if (itemSon.handle == 'ToolVariableSetting') {
                item.data.parameters = [
                  {
                    ...itemSon,
                    nextNodeId: edgeItem.target,
                  },
                ];
              } else if (itemSon.id === 'ToolLLM') {
                item.data.parameters.push({
                  prompt: item.data.customizForms?.[0].prompt, // 提示词
                  isAIGC: item.data.customizForms?.[0].isAIGC, // 是否AIGC （0否，1是）
                  content: item.data.customizForms?.[0].content, //  AIGC
                  resTyep: item.data.customizForms?.[0].resTyep, // 返回类型（暂时只有文本）
                  variablesKey: item.data.customizForms?.[0].variablesKey, // 存储变量
                  nextNodeId: edgeItem.target,
                  contentShow: item.data.customizForms?.[0].contentShow, // 是否显示返回结果
                });
              } else if (itemSon.id === 'ConditionCheck') {
                if (item.data.parameters.length == 0) {
                  item.data.parameters.push({
                    condition: [
                      {
                        ...itemSon,
                        nextNodeId: edgeItem.target,
                      },
                    ],
                    defaultNodeId: '',
                  });
                } else {
                  item.data.parameters[0].condition.push({
                    ...itemSon,
                    nextNodeId: edgeItem.target,
                  });
                }
              } else if (itemSon.id === 'AskQuestionCard') {
                item.data.parameters = [
                  {
                    ...itemSon,
                    nextNodeId: edgeItem.target,
                  },
                ];
              } else if (itemSon.id === 'ToolDelay') {
                item.data.parameters = [
                  {
                    ...itemSon,
                    nextNodeId: edgeItem.target,
                  },
                ];
              } else if (itemSon.id === 'ConditionCheckElse') {
                if (item.data.parameters.length == 0) {
                  item.data.parameters.push({
                    condition: [],
                    defaultNodeId: edgeItem.target,
                  });
                } else {
                  item.data.parameters[0].defaultNodeId = edgeItem.target;
                }
              } else if (itemSon.id === 'Fallback') {
                console.log(
                  'zhenghe>>>fallback button',
                  JSON.stringify(itemSon),
                );
                item.data.fallback = edgeItem.target;
              } else if (itemSon.id === 'Warning') {
                console.log('toolrag>>>>', itemSon);
                if (item.data.parameters.length == 0) {
                  item.data.parameters.push({
                    lackKnowledgeNextNodeId: edgeItem.target,
                  });
                } else {
                  item.data.parameters[0].lackKnowledgeNextNodeId =
                    edgeItem.target;
                }
                console.log('toolrag>>>>item', item);
              } else if (itemSon.handle == 'AskQuestionText') {
                console.log('zhenghe>>askText', item);
                item.data.parameters.push({
                  ...itemSon,
                  nextNodeId: edgeItem.target,
                });
              } else if (
                itemSon.handle &&
                itemSon.handle.indexOf('AskQuestionButton') != -1
              ) {
                console.log('zhenghe>>>button', itemSon, item);
                itemSon.buttonName = itemSon.content;
                itemSon.buttonValue = itemSon.value;
                itemSon.nextNodeId = edgeItem.target;
                if (item.data.parameters.length == 0) {
                  let str = item.data.customizForms[0].content;
                  let displayType = item.data.customizForms[0].displayType;
                  let saveSelectedToAttr =
                    item.data.customizForms[0].saveSelectedToAttr;
                  item.data.parameters.push({
                    content: str,
                    displayType: displayType,
                    saveSelectedToAttr: saveSelectedToAttr,
                    buttonList: [itemSon],
                  });
                } else {
                  item.data.parameters[0].buttonList?.push(itemSon);
                }
              } else if (itemSon.id == 'MessageHotIssue') {
                console.log('zhenghe>>>MessageHotIssue', itemSon, item);
                item.data.parameters = [
                  { ...itemSon, nextNodeId: edgeItem.target },
                ];
                // if (item.data.parameters.length == 0) {
                //   item.data.parameters.push(itemSon);
                // } else {
                //   item.data.parameters
                // }
              } else if (
                itemSon.id == 'ToolUpdateCustomer' &&
                itemSon.handle &&
                itemSon.handle.indexOf('ToolUpdateCustomer') != -1
              ) {
                console.log('zhenghe>>>ToolUpdateCustomer', itemSon, item);
                let arr = item.data.customizForms.filter(
                  item => item.id != 'Fallback',
                );
                console.log('zhenghe>>>ToolUpdateCustomer', arr);
                if (item.data.parameters.length == 0) {
                  item.data.parameters = [
                    {
                      attributes: arr.map(item => ({
                        attributeName: item.attributeName,
                        attributeValue: item.attributeValue,
                      })),
                      nextNodeId: edgeItem.target,
                    },
                  ];
                } else {
                  item.data.parameters[0].attributes = arr.map(item => ({
                    attributeName: item.attributeName,
                    attributeValue: item.attributeValue,
                  }));
                  item.data.parameters[0].nextNodeId = edgeItem.target;
                }
              } else if (
                itemSon.id == 'ToolUpdateTicket' &&
                itemSon.handle &&
                itemSon.handle.indexOf('ToolUpdateTicket') != -1
              ) {
                // console.log('zhenghe>>>ToolUpdateTicket', itemSon, item);
                let arr = item.data.customizForms.filter(
                  item => item.id != 'Fallback',
                );
                // console.log('zhenghe>>>ToolUpdateTicket', arr);
                if (item.data.parameters.length == 0) {
                  item.data.parameters = [
                    {
                      attributes: arr.map(item => ({
                        attributeName: item.attributeName,
                        attributeValue: item.attributeValue,
                      })),
                      nextNodeId: edgeItem.target,
                    },
                  ];
                } else {
                  item.data.parameters[0].attributes = arr.map(item => ({
                    attributeName: item.attributeName,
                    attributeValue: item.attributeValue,
                  }));
                  item.data.parameters[0].nextNodeId = edgeItem.target;
                }
              } else if (
                itemSon.id == 'AskQuestionText' &&
                itemSon.handle == 'AskQuestionLLM'
              ) {
                item.data.parameters.push({
                  content: itemSon.content,
                  attributes: item.data.customizForms.filter(
                    item => item.id == 'AskQuestionLLM',
                  ),
                  nextNodeId: edgeItem.target,
                });
                console.log('zhenghe>>>llm', item.data);
              } else if (
                itemSon.id == 'AskQuestionLLM' &&
                itemSon.handle == 'AskQuestionLLM'
              ) {
                return;
              } else if (itemSon.id == 'Form') {
                if (item.data.parameters.length == 0) {
                  item.data.parameters.push({ formList: [itemSon] });
                } else {
                  item.data.parameters.formList.push(itemSon);
                }
              } else if (itemSon.handle == 'AskQuestionFormButton1') {
                if (item.data.parameters.length == 0) {
                  item.data.parameters.push({
                    submitNextNodeId: edgeItem.target,
                  });
                } else {
                  item.data.parameters[0].submitNextNodeId = edgeItem.target;
                }
                let arr = item.data.customizForms.filter(
                  item => item.id == 'Form',
                );
                item.data.parameters[0].formList = arr;
              } else if (itemSon.handle == 'AskQuestionFormButton2') {
                if (item.data.parameters.length == 0) {
                  item.data.parameters.push({
                    cancelNextNodeId: edgeItem.target,
                  });
                } else {
                  item.data.parameters[0].cancelNextNodeId = edgeItem.target;
                }
              } else if (
                itemSon.id == 'handle' &&
                itemSon.handle &&
                typeof itemSon.handle == 'string' &&
                itemSon.handle.indexOf('ConditionIntent') != -1
              ) {
                itemSon.nextNodeId = edgeItem.target;
                item.data.parameters.push(itemSon);
              } else if (
                itemSon.id == 'handle' &&
                itemSon.handle == 'ToolWorkHours'
              ) {
                if (item.data.parameters.length == 0) {
                  item.data.parameters = [
                    {
                      workTimeId: itemSon.workTimeId, // 工作时间ID
                      notNextNodeId: '', // 不在工作时间下一个节点
                      nextNodeId: edgeItem.target, // 正常工作时间下一个节点
                    },
                  ];
                } else {
                  item.data.parameters[0].workTimeId = itemSon.workTimeId;
                  item.data.parameters[0].nextNodeId = edgeItem.target;
                }
              } else if (
                itemSon.id == 'handle' &&
                itemSon.handle == 'ToolWorkHoursNot'
              ) {
                if (item.data.parameters.length == 0) {
                  item.data.parameters = [
                    {
                      workTimeId: '', // 工作时间ID
                      notNextNodeId: edgeItem.target, // 不在工作时间下一个节点
                      nextNodeId: '', // 正常工作时间下一个节点
                    },
                  ];
                } else {
                  item.data.parameters[0].notNextNodeId = edgeItem.target;
                }
              } else if (itemSon.id == 'Knowlege') {
                if (item.data.parameters.length == 0) {
                  item.data.parameters = [
                    {
                      ...itemSon,
                      nextNodeId: edgeItem.target,
                    },
                  ];
                  console.log(
                    'tooragwxr>>>no',
                    JSON.stringify({
                      ...itemSon,
                      nextNodeId: edgeItem.target,
                    }),
                  );
                } else {
                  let arr = JSON.parse(JSON.stringify(item.data.parameters[0]));
                  item.data.parameters[0] = {
                    ...arr,
                    restQuestion: itemSon.restQuestion, // 重写用户问题
                    useFaq: itemSon.useFaq, // FAQ 知识库（勾选为1 ，不勾选为0）
                    useQa: itemSon.useQa, // RAG 知识库（勾选为1 ，不勾选为0）
                    knowledgeId: itemSon.knowledgeId, // 知识库ID
                    moodBoardFlag: itemSon.moodBoardFlag, // 问答风格 0 专业 1温柔
                    lackKnowledge: itemSon.lackKnowledge, // 缺乏知识回答
                    tags: itemSon.tags, //知识标签
                    nextNodeId: edgeItem.target, // 下一个节点
                  };
                  console.log(
                    'tooragwxr>>>yes',
                    JSON.stringify({
                      ...arr,
                      restQuestion: itemSon.restQuestion, // 重写用户问题
                      useFaq: itemSon.useFaq, // FAQ 知识库（勾选为1 ，不勾选为0）
                      useQa: itemSon.useQa, // RAG 知识库（勾选为1 ，不勾选为0）
                      knowledgeId: itemSon.knowledgeId, // 知识库ID
                      moodBoardFlag: itemSon.moodBoardFlag, // 问答风格 0 专业 1温柔
                      lackKnowledge: itemSon.lackKnowledge, // 缺乏知识回答
                      tags: itemSon.tags, //知识标签
                      nextNodeId: edgeItem.target, // 下一个节点
                    }),
                  );
                }
              } else if (itemSon.id == 'MessageDoc') {
                item.data.parameters = [
                  {
                    content: itemSon.label,
                    nextNodeId: edgeItem.target,
                    url: itemSon.url,
                    fileName: itemSon.fileName,
                    nextNodeId: edgeItem.target,
                  },
                ];
              } else {
                item.data.parameters.push({
                  content: itemSon.label,
                  nextNodeId: edgeItem.target,
                  url: itemSon.url,
                });
              }
            }
          });
        } else if (edgeItem.source) {
          //默认连接点情况赋值parameters
          item.data.customizForms?.forEach(itemSon => {
            if (edgeItem.source === item.id) {
              //默认连接点情况赋值parameters 开始节点单独处理
              if (item.data.componentType === 'Start') {
                console.log('itemSon>>>>startNode', item);
                setIntentId(itemSon.currentIntentInfo?.intentId);
                newIntentId = itemSon.currentIntentInfo?.intentId;
                let intentAttribute = {};
                itemSon.currentIntentInfo?.attributesList?.forEach(
                  (e, index) => {
                    console.log('startNode>>>attributesListitem>>>>', e);
                    // let keyValue =
                    //   itemSon.variableList[index]?.type === 1
                    //     ? 'var_system.' + itemSon.variableList[index].variableName
                    //     : itemSon.variableList[index]?.type === 2
                    //     ? 'var_session.' +
                    //       itemSon.variableList[index]?.variableName
                    //     : itemSon.variableList[index]?.type === 3
                    //     ? 'var_agent.' + itemSon.variableList[index].variableName
                    //     : itemSon.variableList[index]?.type === 4
                    //     ? 'var_global.' + itemSon.variableList[index].variableName
                    //     : '';
                    intentAttribute[e.attributeValues] = '{' + e.varId + '}';
                  },
                );
                console.log(
                  'startNode>>>attributesListitem>>>>',
                  intentAttribute,
                );
                item.data.parameters.push({
                  triggerType: 1, // 1:意图 2: 其他智能体 3:没有
                  intentId: itemSon.currentIntentInfo?.intentId, //意图id
                  scriptType: itemSon.currentIntentInfo?.scriptType, //话术类型 1.静态话术 2.动态话术
                  intentAttribute: intentAttribute,
                  nextNodeId: edgeItem.target,
                });
              } else {
                item.data.parameters.push({
                  content: itemSon.label,
                  nextNodeId: edgeItem.target,
                  url: itemSon.url,
                });
              }
            }
          });
        }
      });
      //处理末尾节点
      if (
        item.data.parameters.length === 0 ||
        item.data.componentType == 'AskQuestionButton' ||
        item.data.componentType == 'ToolRag' ||
        item.data.componentType == 'ConditionCheck' ||
        item.data.componentType == 'ConditionCheckElse'
      ) {
        if (item.data.componentType === 'ToolToAgent') {
          item.data.parameters.push({
            content: item.data.customizForms?.[0].label, // 默认消息
            judgmentRuleType: item.data.customizForms?.[0].judgmentRuleType, // 分配规则    1-系统自动分配   2-人工指定
            distributionRuleType:
              item.data.customizForms?.[0].distributionRuleType, //  1-分配给特定团队；2-分配给特定坐席
            distributionIds: item.data.customizForms?.[0].distributionIds?.join(
              ',',
            ), // 分配指定的坐席或者团队 ids  逗号拼接
            workRecordTypeCode: item.data.customizForms?.[0].workRecordTypeCode, //  工单类型code
            priorityLevelId: item.data.customizForms?.[0].priorityLevelId, // 优先级id
            customerLabel: item.data.customizForms?.[0].customerLabel, // 客户标签ID，以逗号分隔,
            intent: item.data.customizForms?.[0].intent, // 进线意图
            nextNodeId: '',
          });
        } else if (item.data.componentType === 'ToolLLM') {
          item.data.parameters.push({
            prompt: item.data.customizForms?.[0].prompt, // 提示词
            isAIGC: item.data.customizForms?.[0].isAIGC, // 是否AIGC （0否，1是）
            content: item.data.customizForms?.[0].content, //  AIGC
            resTyep: item.data.customizForms?.[0].resTyep, // 返回类型（暂时只有文本）
            variablesKey: item.data.customizForms?.[0].variablesKey, // 存储变量
            nextNodeId: '',
            contentShow: item.data.customizForms?.[0].contentShow, // 是否显示返回结果
          });
        } else if (
          item.data.componentType == 'AskQuestionButton' ||
          item.data.componentType == 'AskQuestionForm' ||
          item.data.componentType == 'AskQuestionText' ||
          item.data.componentType == 'ConditionIntent' ||
          item.data.componentType == 'ToolRag' ||
          item.data.componentType == 'MessageDoc' ||
          item.data.componentType == 'ToolWorkHours' ||
          item.data.componentType == 'ToolWorkHoursNot' ||
          item.data.componentType == 'ToolAPI' ||
          item.data.componentType == 'ToolVariableSetting' ||
          item.data.componentType == 'ToolUpdateCustomer' ||
          item.data.componentType == 'ToolUpdateTicket' ||
          item.data.componentType == 'ConditionCheck' ||
          item.data.componentType == 'ConditionCheckElse' ||
          item.data.componentType == 'ToolSetCustomerTag' ||
          item.data.componentType == 'AskQuestionLLMInfoCollection' ||
          item.data.componentType == 'MessageHotIssue' ||
          item.data.componentType == 'AskQuestionCard' ||
          item.data.componentType == 'ToolDelay'
        ) {
          item.data.customizForms?.forEach(itemSon => {
            if (itemSon.id === 'Warning') {
              console.log('toolrag>>>single', itemSon);
              if (item.data.parameters.length == 0) {
                item.data.parameters.push({
                  lackKnowledgeNextNodeId: '',
                });
              } else {
                if (!item.data.parameters[0].lackKnowledgeNextNodeId) {
                  item.data.parameters[0].lackKnowledgeNextNodeId = '';
                }
              }
              console.log('toolrag>>>single', item);
            } else if (itemSon.id == 'Fallback') {
              console.log('zhenghe>>>fallback', JSON.stringify(item));
              if (!item.data.fallback) {
                item.data.fallback = '';
              }
            } else if (itemSon.id == 'MessageHotIssue') {
              console.log('zhenghe>>>MessageHotIssue', itemSon, item);
              item.data.parameters = [{ ...itemSon, nextNodeId: '' }];
            } else if (itemSon.id == 'AskQuestionCard') {
              console.log('zhenghe>>>AskQuestionCard', itemSon, item);
              item.data.parameters = [{ ...itemSon, nextNodeId: '' }];
            } else if (itemSon.id === 'ToolDelay') {
              item.data.parameters = [
                {
                  ...itemSon,
                  nextNodeId: '',
                },
              ];
            } else if (itemSon.id === 'ConditionCheck') {
              if (item.data.parameters.length == 0) {
                item.data.parameters.push({
                  condition: [
                    {
                      ...itemSon,
                      nextNodeId: '',
                    },
                  ],
                  defaultNodeId: '',
                });
              } else {
                if (
                  item.data.parameters[0].condition.findIndex(e => {
                    return e.index == itemSon.index;
                  }) == -1
                ) {
                  item.data.parameters[0].condition.push({
                    ...itemSon,
                    nextNodeId: '',
                  });
                }
              }
            } else if (itemSon.id == 'ToolSetCustomerTag') {
              item.data.parameters = [
                {
                  tagList: item.data.customizForms?.[0].tagList,
                  nextNodeId: '',
                },
              ];
            } else if (
              itemSon.id == 'ToolUpdateCustomer' &&
              itemSon.handle &&
              itemSon.handle.indexOf('ToolUpdateCustomer') != -1
            ) {
              // console.log('zhenghe>>>ToolUpdateCustomer', itemSon, item);
              let arr = item.data.customizForms.filter(
                item => item.id != 'Fallback',
              );
              if (item.data.parameters.length == 0) {
                item.data.parameters = [
                  {
                    attributes: arr.map(item => ({
                      attributeName: item.attributeName,
                      attributeValue: item.attributeValue,
                    })),
                    nextNodeId: '',
                  },
                ];
              } else {
                item.data.parameters[0].attributes = arr.map(item => ({
                  attributeName: item.attributeName,
                  attributeValue: item.attributeValue,
                }));
                item.data.parameters[0].nextNodeId = '';
              }
            } else if (
              itemSon.id == 'ToolUpdateTicket' &&
              itemSon.handle &&
              itemSon.handle.indexOf('ToolUpdateTicket') != -1
            ) {
              // console.log('zhenghe>>>ToolUpdateTicket', itemSon, item);
              let arr = item.data.customizForms.filter(
                item => item.id != 'Fallback',
              );
              // console.log('zhenghe>>>ToolUpdateTicket', arr);
              if (item.data.parameters.length == 0) {
                item.data.parameters = [
                  {
                    attributes: arr.map(item => ({
                      attributeName: item.attributeName,
                      attributeValue: item.attributeValue,
                    })),
                    nextNodeId: '',
                  },
                ];
              } else {
                item.data.parameters[0].attributes = arr.map(item => ({
                  attributeName: item.attributeName,
                  attributeValue: item.attributeValue,
                }));
                item.data.parameters[0].nextNodeId = '';
              }
            } else if (itemSon.handle == 'ToolAPI' && itemSon.id == 'ToolAPI') {
              item.data.parameters = [
                {
                  apiId: itemSon.apiId,
                  variablesKey: itemSon.variablesKey,
                  nextNodeId: '',
                },
              ];
            } else if (itemSon.handle == 'ToolVariableSetting') {
              item.data.parameters = [
                {
                  ...itemSon,
                  nextNodeId: '',
                },
              ];
            } else if (
              itemSon.id == 'handle' &&
              itemSon.handle == 'ToolWorkHours'
            ) {
              if (item.data.parameters.length == 0) {
                item.data.parameters = [
                  {
                    workTimeId: itemSon.workTimeId, // 工作时间ID
                    notNextNodeId: '', // 不在工作时间下一个节点
                    nextNodeId: '', // 正常工作时间下一个节点
                  },
                ];
              } else {
                item.data.parameters[0].workTimeId = itemSon.workTimeId;
                item.data.parameters[0].nextNodeId = '';
              }
            } else if (
              itemSon.id == 'handle' &&
              itemSon.handle == 'ToolWorkHoursNot'
            ) {
              if (item.data.parameters.length == 0) {
                item.data.parameters = [
                  {
                    workTimeId: '', // 工作时间ID
                    notNextNodeId: '', // 不在工作时间下一个节点
                    nextNodeId: '', // 正常工作时间下一个节点
                  },
                ];
              } else {
                item.data.parameters[0].notNextNodeId = '';
              }
            } else if (itemSon.handle == 'AskQuestionText') {
              item.data.parameters.push({
                ...itemSon,
                nextNodeId: '',
              });
            } else if (
              itemSon.id == 'AskQuestionText' &&
              itemSon.handle == 'AskQuestionLLM'
            ) {
              item.data.parameters.push({
                content: itemSon.content,
                attributes: item.data.customizForms.filter(
                  item => item.id == 'AskQuestionLLM',
                ),
                nextNodeId: '',
              });
              console.log('zhenghe>>>llm', item.data);
            } else if (
              itemSon.id == 'AskQuestionLLM' &&
              itemSon.handle == 'AskQuestionLLM'
            ) {
              return;
            } else if (
              itemSon.handle &&
              itemSon.handle.indexOf('AskQuestionButton') != -1
            ) {
              console.log('zhenghe>>>button noline', itemSon, item);
              if (item.data.parameters.length == 0) {
                itemSon.buttonName = itemSon.content;
                itemSon.buttonValue = itemSon.value;
                itemSon.nextNodeId = '';
                let str = item.data.customizForms[0].content;
                let displayType = item.data.customizForms[0].displayType;
                let saveSelectedToAttr =
                  item.data.customizForms[0].saveSelectedToAttr;
                item.data.parameters.push({
                  content: str,
                  displayType: displayType,
                  saveSelectedToAttr: saveSelectedToAttr,
                  buttonList: [itemSon],
                });
              } else {
                if (
                  item.data.parameters[0].buttonList.findIndex(e => {
                    return (
                      e.handle == itemSon.handle &&
                      e.buttonName == itemSon.buttonName
                    );
                  }) == -1
                ) {
                  itemSon.buttonName = itemSon.content;
                  itemSon.buttonValue = itemSon.value;
                  itemSon.nextNodeId = '';
                  item.data.parameters[0].buttonList?.push(itemSon);
                }
              }
            } else if (itemSon.id == 'Form') {
              console.log('zhenghe>>>sing>>>form', item.data.parameters);
              if (item.data.parameters.length == 0) {
                item.data.parameters.push({ formList: [itemSon] });
              } else {
                item.data.parameters.formList?.push(itemSon);
              }
            } else if (itemSon.handle == 'AskQuestionFormButton1') {
              if (item.data.parameters.length == 0) {
                item.data.parameters.push({
                  submitNextNodeId: '',
                });
              } else {
                item.data.parameters[0].submitNextNodeId = '';
              }
              let arr = item.data.customizForms.filter(
                item => item.id == 'Form',
              );
              item.data.parameters[0].formList = arr;
            } else if (itemSon.handle == 'AskQuestionFormButton2') {
              if (item.data.parameters.length == 0) {
                item.data.parameters.push({
                  cancelNextNodeId: '',
                });
              } else {
                item.data.parameters[0].cancelNextNodeId = '';
              }
            } else if (
              itemSon.id == 'handle' &&
              itemSon.handle &&
              typeof itemSon.handle == 'string' &&
              itemSon.handle.indexOf('ConditionIntent') != -1
            ) {
              itemSon.nextNodeId = '';
              item.data.parameters.push(itemSon);
            } else if (itemSon.id == 'Knowlege') {
              console.log('toolrag>>>>11111', item);
              console.log('toolrag>>>>11111', itemSon);
              if (item.data.parameters.length == 0) {
                item.data.parameters = [
                  {
                    ...itemSon,
                    nextNodeId: '',
                  },
                ];
              } else if (!item.data.parameters[0].nextNodeId) {
                let arr = JSON.parse(JSON.stringify(item.data.parameters[0]));
                item.data.parameters[0] = {
                  lackKnowledgeNextNodeId: arr.lackKnowledgeNextNodeId,
                  restQuestion: itemSon.restQuestion, // 重写用户问题
                  useFaq: itemSon.useFaq, // FAQ 知识库（勾选为1 ，不勾选为0）
                  useQa: itemSon.useQa, // RAG 知识库（勾选为1 ，不勾选为0）
                  knowledgeId: itemSon.knowledgeId, // 知识库ID
                  moodBoardFlag: itemSon.moodBoardFlag, // 问答风格 0 专业 1温柔
                  lackKnowledge: itemSon.lackKnowledge, // 缺乏知识回答
                  nextNodeId: '', // 下一个节点
                  tags: itemSon.tags, //知识标签
                };
              }
            } else if (itemSon.id == 'MessageDoc') {
              item.data.parameters = [
                {
                  content: itemSon.label,
                  nextNodeId: '',
                  url: itemSon.url,
                  fileName: itemSon.fileName,
                },
              ];
            }
          });
        } else if (item.data.componentType === 'Start') {
          let itemSon = item.data.customizForms[0];
          setIntentId(itemSon.currentIntentInfo?.intentId);
          newIntentId = itemSon.currentIntentInfo?.intentId;
          let intentAttribute = {};
          itemSon.currentIntentInfo?.attributesList?.forEach((e, index) => {
            console.log('startNode>>>attributesListitem>>>>', e);
            // let keyValue =
            //   itemSon.variableList[index]?.type === 1
            //     ? 'var_system.' + itemSon.variableList[index].variableName
            //     : itemSon.variableList[index]?.type === 2
            //     ? 'var_session.' +
            //       itemSon.variableList[index]?.variableName
            //     : itemSon.variableList[index]?.type === 3
            //     ? 'var_agent.' + itemSon.variableList[index].variableName
            //     : itemSon.variableList[index]?.type === 4
            //     ? 'var_global.' + itemSon.variableList[index].variableName
            //     : '';
            intentAttribute[e.attributeValues] = '{' + e.varId + '}';
          });
          item.data.parameters.push({
            triggerType: 1, // 1:意图 2: 其他智能体 3:没有
            intentId: itemSon.currentIntentInfo?.intentId, //意图id
            scriptType: itemSon.currentIntentInfo?.scriptType, //话术类型 1.静态话术 2.动态话术
            intentAttribute: intentAttribute,
            nextNodeId: '',
          });
        } else {
          console.log('zhenghe>>>fallback', item);
          item.data.customizForms?.forEach(itemSon => {
            if (itemSon.id !== 'Fallback') {
              console.log('tooragwxr>>>fall');
              item.data.parameters.push({
                content: itemSon.label,
                nextNodeId: '',
                url: itemSon.url,
              });
            } else {
              console.log('zhenghe>>>fallback', itemSon);
              item.data.fallback = '';
            }
          });
        }
      }
    });
    let res = verificationForm(newNodes.nodesList);
    if (!res) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'ai.agent.nodes.header.save.error.formCancle',
        }),
      });
      return false;
    } else {
      setFinishJson(newNodes);
      return { nodes: newNodes, intentId: newIntentId };
    }
  };
  const handleMarketingTemplate = nodes => {
    return Promise.resolve({
      ...nodes, // 复制 nodes 的其他属性
      nodesList: nodes.nodesList.map(item => {
        // 如果满足条件，复制到 parameters[0] 后端处理用
        if (
          item.data.componentType === 'MarketingMessage' &&
          item.data.customizForms
        ) {
          return {
            ...item,
            data: {
              ...item.data,
              parameters: [
                {
                  ...item.data.parameters[0], // 复制 parameters[0] 的其他属性
                  ...item.data.customizForms[0], // 合并 customizForms[0]
                },
                ...item.data.parameters.slice(1), // 保留 parameters 数组中后续的元素
              ],
            },
          };
        }
        // 不满足条件则返回原始 item
        return item;
      }),
    });
  };
  //保存智能体
  const AiAgentSave = async value => {
    let newNodes = getFinishJson();
    if (!newNodes) return;

    // 处理parameters 中没有营销模版 特殊处理
    const nodes = await handleMarketingTemplate(newNodes.nodes);
    console.log(nodes, edges, newNodes, JSON.stringify(newNodes), '保存的JSON');
    const templateUuid = getTemplateUuidFromNodes();
    setLoadingSave(true);
    dispatch({
      type: 'aiagent/AiAgentSave',
      payload: {
        aiAgentId: aiAgentId, //智能体ID
        json: nodes, //流程对应的json
        intentId: newNodes.intentId,
        channelIds: aiAgentInfo.channelIds,
        templateUuid, // 传递 templateUuid
        channelTypeId: aiAgentInfo.channelTypeId, //1:聊天，2:邮件，3:电话
      },
      callback: response => {
        let { code, data, msg } = response;
        setLoadingSave(false);
        if (200 === code) {
          setSaveFlag(true);
          if (aiAgentStatus === 'copy') {
            createNewAiAgentInfo(newNodes);
          } else {
            notification.success({
              message: getIntl().formatMessage({
                id: 'customer.ext.info.save.success',
              }),
            });
          }
          if (value === 'back') {
            goBack();
          }
          getDeployVersion();
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  //切换已部署的版本
  const handleDeployVersion = e => {
    console.log(e, 'AiAgentInfo');
    dispatch({
      type: 'aiagent/AiAgentInfo',
      payload: {
        deployId: e.key,
      },
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          setNodes(data.nodesList);
          setEdges(data.edgeList);
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  //获得智能体部署版本
  const menuProps = {
    items: dropdownItem,
    onClick: handleDeployVersion,
  };
  const getDeployVersion = () => {
    dispatch({
      type: 'aiagent/getAiAgentDeployList',
      payload: {
        aiAgentId: aiAgentId, //智能体ID
      },
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          let items = data?.map((item, index) => {
            let deployStatus =
              item.deployStatus === 1
                ? getIntl().formatMessage({
                    id: 'ai.agent.nodes.header.tips.status.3',
                  })
                : getIntl().formatMessage({
                    id: 'ai.agent.nodes.header.tips.status.1',
                  });
            return {
              label: item.deployName + ', ' + deployStatus,
              key: item.deployId,
              icon: index === 0 ? <FlowLatest /> : '',
              deployStatus: item.deployStatus,
            };
          });
          setDropdownItem(items);
          setCurrentVersion(items[0]);
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  //保存并部署
  const AiAgentSaveAndDeploy = async () => {
    console.log(
      aiAgentInfo,
      aiAgentData,
      '保存部署数据aiAgentInfo，aiAgentData====',
    );
    let flag = verificationFlow();
    if (!flag) {
      return;
    }
    let newNodes = getFinishJson();
    if (newNodes == false) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'ai.agent.nodes.header.save.error.formCancle',
        }),
      });
      return;
    }
    console.log(newNodes, '保存部署数据newNodes====');
    const templateUuid = getTemplateUuidFromNodes();
    setFlowLoading(true);
    setLoadingSaveOrDeploy(true);
    const nodes = await handleMarketingTemplate(newNodes.nodes);
    dispatch({
      type: 'aiagent/AiAgentSaveAndDeploy',
      payload: {
        aiAgentId: aiAgentId, //智能体ID
        json: nodes, //流程对应的json
        intentId: newNodes.intentId,
        channelIds: aiAgentInfo.channelIds,
        templateUuid, // 传递 templateUuid
        channelTypeId: aiAgentInfo.channelTypeId,
      },
      callback: response => {
        let { code, data, msg } = response;
        setFlowLoading(false);
        setLoadingSaveOrDeploy(false);
        if (200 === code) {
          if (aiAgentStatus === 'copy') {
            createNewAiAgentInfo(newNodes);
          } else {
            notification.success({
              message: getIntl().formatMessage({
                id: 'ai.agent.nodes.header.save.success',
              }),
            });
          }
          setDeployFlag(true);
          goBack();
          // getDeployVersion();
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  const verificationForm = nodesList => {
    console.log('校验form表单', nodesList);
    let res = true;
    let arr = nodesList.filter(e =>
      e.data?.componentType?.includes('AskQuestionForm'),
    );
    console.log('formArr', arr);
    arr.forEach(e => {
      console.log('e', e);
      if (!e.data.parameters[0].cancelNextNodeId) {
        res = false;
      }
    });
    console.log('校验form表单', res);
    return res;
  };
  //校验流程
  const verificationFlow = () => {
    console.log('校验流程');
    let nodesList = [...nodes];
    let edgeList = [...edges];
    let result = true;
    //开始节点id
    let initID = nodesList[0].id;
    //校验开始节点是否有连接isRequirezisisisisdsafdsfd
    let flag = false;
    edgeList?.forEach((edge, index) => {
      if (edge.source === initID && edge.target) {
        flag = true;
      }
    });
    if (edgeList?.length < 1 || !flag) {
      result = false;
      notification.error({
        message: getIntl().formatMessage({
          id: 'ai.agent.verification.start',
        }),
      });
      return result;
    }
    //校验每一个节点的自定义内容是否有必填项没填
    nodesList?.forEach((node, index) => {
      if (node.data?.isRequire) {
        let isRequireData = node.data.isRequire;
        let customizForms = node.data?.customizForms;
        for (let key in isRequireData) {
          if (customizForms) {
            for (let form of customizForms) {
              if (form[key] !== undefined && !form[key].toString()) {
                notification.error({
                  message: isRequireData[key],
                });
                result = false;
                break;
              }
            }
          }
        }
      }
    });
    return result;
  };

  const queryCurrentVarRef = () => {
    setVariableName('');
    setIsModalOpenVariables(true);
  };
  const handleAddVarItRef = () => {
    tableVarRef.current?.handleAddVarIt();
  };
  //返回列表页
  const goBack = () => {
    localStorage.setItem('aiAgentId', '');
    localStorage.setItem('aiAgentType', '');
    localStorage.setItem('aiAgentStatus', '');
    localStorage.setItem('aiAgentData', '');
    setSaveFlag(true);
    history.replace({
      pathname: '/marketingAgent',
    });
  };
  //验证是否已经保存
  const handleVerifySave = event => {
    if (saveFlag) {
      goBack();
    } else {
      setIsModalOpenBack(true);
    }
  };

  useEffect(() => {
    if (detailRefresh) {
      console.log(aiAgentInfo, '这是aiAgentInfo');
      setDeployFlag(aiAgentInfo.deployStatus == 1 ? true : false);
    }
  }, [detailRefresh]);
  const handleVerifySave2 = event => {
    // if (saveFlag) {
    //   console.log('zoulelele11');
    //   goBack();
    // } else {
    //   console.log('zoulelele2');
    //   event.preventDefault(); // 阻止默认的返回操作
    //   setIsModalOpenBack(true);
    // }
  };
  // useEffect(() => {
  //   window.addEventListener('popstate', event => {
  //     if (!saveFlag) {
  //       console.log(saveFlag, event, 'zoulele0');
  //       event.preventDefault();
  //       history.replace({
  //         pathname: '/agentFlow',
  //       });
  //     }
  //   });
  //   // return () => {
  //   //   window.removeEventListener('popstate', handleVerifySave2);
  //   // };
  // }, [saveFlag]);
  // useEffect(() => {
  //   const unlisten = history.listen((location, action) => {
  //     if (action === 'POP') {
  //       if (saveFlag) {
  //         console.log('zoulelele11');
  //         goBack();
  //       } else {
  //         console.log('zoulelele2');
  //         history.replace({
  //           pathname: '/agentFlow',
  //         });
  //         setIsModalOpenBack(true);
  //       }
  //       // if (!confirmLeave) {
  //       //   history.push(location.pathname); // 重新导航到当前页面
  //       // }
  //     }
  //   });

  //   return () => {
  //     unlisten(); // 清理监听器
  //   };
  // }, [history, saveFlag]);
  useEffect(() => {
    if (routeInfo?.status === 'create' || routeInfo?.status === 'editor') {
      setAiAgentId(routeInfo?.aiAgentId);
      setAiAgentStatus(routeInfo?.status);
    } else if (routeInfo?.status === 'copy') {
      setAiAgentId(routeInfo?.aiAgentData.aiAgentId);
      setAiAgentData(routeInfo?.aiAgentData);
      setAiAgentStatus(routeInfo?.status);
      localStorage.setItem('aiAgentId', routeInfo?.aiAgentData.aiAgentId);
    } else {
      setAiAgentId(localStorage.getItem('aiAgentId'));
      setAiAgentStatus(localStorage.getItem('aiAgentStatus'));
      setAiAgentData(
        localStorage.getItem('aiAgentData')
          ? JSON.parse(localStorage.getItem('aiAgentData'))
          : {},
      );
    }
  }, []);
  useEffect(() => {
    //status：‘create’创建
    //status：‘copy’复制
    //status：‘editor’修改
    if (aiAgentStatus === 'create' || aiAgentStatus === 'editor') {
      // getIntentGrouped();
      getAiAgentDetail(aiAgentId);
      getDeployVersion();
    } else if (aiAgentStatus === 'copy') {
      //替换id和nodeId
      copyAiAgent();
      setDropdownItem([]);
    }
  }, [aiAgentId, aiAgentStatus, aiAgentData]);

  useEffect(() => {
    tableVarRef.current?.queryCurrentVar();
  }, [isModalOpenVariables]);
  const onPressEnter = () => {
    tableVarRef.current?.queryCurrentVar();
  };
  useEffect(() => {
    if (isModalOpenSetting) {
      queryReminderList();
    }
  }, [isModalOpenSetting]);
  useEffect(() => {
    console.log(nodes, edges, '这是节点');
    setDeployFlag(false);
    setSaveFlag(false);
  }, [nodes, edges]);
  useEffect(() => {
    setFlowLoading2(true);
    setTimeout(() => {
      console.log(currentVersion, '这是节点2');
      if (currentVersion?.deployStatus === 1) {
        setDeployFlag(true);
      } else {
        setDeployFlag(false);
      }
      setFlowLoading2(false);
    }, 5000);
  }, [currentVersion]);
  const updateAiAgentName = () => {
    setFlowLoading(true);
    dispatch({
      type: 'aiagent/updateAiAgentName',
      payload: {
        aiAgentId: aiAgentId, //智能体ID
        aiAgentName: aiAgentNameTemp,
      },
      callback: response => {
        let { code, data, msg } = response;
        setFlowLoading(false);
        if (200 === code) {
          setAgentNameEditing(false);
          let info = { ...aiAgentInfo };
          info.aiAgentName = aiAgentNameTemp;
          setAiAgentInfo(info);
          notification.success({
            message: msg,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  // 查询意图提醒设置列表
  const queryReminderList = () => {
    setLoadingSetting(true);
    let params = {
      aiAgentId: aiAgentId, //智能体ID
    };
    dispatch({
      type: 'aiagent/queryReminderList',
      payload: params,
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          setLoadingSetting(false);
          setWaitSettingList(response.data);
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  // 设置调用接口显示loading
  const handleChangeLoading = status => {
    setLoadingSetting(status);
  };
  //修改渠道
  const onFinish = values => {
    setLoadingBtn(true);

    dispatch({
      type: 'aiagent/updateAiAgentChannel',
      payload: {
        aiAgentId: aiAgentId, //智能体ID
        channelIds: values.intelligentAgentChannelType?.join(','),
      },
      callback: response => {
        let { code, data, msg } = response;
        setLoadingBtn(false);
        if (200 === code) {
          setIsModalOpenChannel(false);
          let info = { ...aiAgentInfo };
          info.channelIds = values.intelligentAgentChannelType?.join(',');
          setAiAgentInfo(info);
          notification.success({
            message: msg,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  const shortcutTipsRender = () => {
    return (
      <div className={styles.shortcut_tips_content}>
        <div className={styles.shortcut_tips_content_title}>
          <FormattedMessage
            id="ai.agent.nodes.header.tips.shortcut.title"
            values={{
              b: (...chunks) => <b>{chunks}</b>,
            }}
          ></FormattedMessage>
        </div>
        <div>
          <FormattedMessage id="ai.agent.nodes.header.tips.shortcut.1"></FormattedMessage>
        </div>
        <div>
          <FormattedMessage id="ai.agent.nodes.header.tips.shortcut.2"></FormattedMessage>
        </div>
      </div>
    );
  };
  const getCurrentKey = key => {
    if (key == 1) {
      setGetCurrentKeyValue(false);
    } else {
      setGetCurrentKeyValue(true);
    }
  };

  // WhatsApp营销测试相关函数
  const handlePhoneNumberChange = (id, field, value) => {
    setPhoneNumbers(prev =>
      prev.map(item => (item.id === id ? { ...item, [field]: value } : item)),
    );
  };

  const addPhoneNumber = () => {
    if (phoneNumbers.length < 5) {
      const newId = Math.max(...phoneNumbers.map(p => p.id)) + 1;
      setPhoneNumbers(prev => [
        ...prev,
        { id: newId, countryCode: '+86', number: '' },
      ]);
    }
  };

  const removePhoneNumber = id => {
    if (phoneNumbers.length > 1) {
      setPhoneNumbers(prev => prev.filter(item => item.id !== id));
    }
  };

  const validatePhoneNumbers = () => {
    const validNumbers = phoneNumbers.filter(item => item.number.trim() !== '');
    if (validNumbers.length === 0) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'marketing.test.phone.required',
          defaultMessage: '请至少输入一个手机号',
        }),
      });
      return false;
    }

    // 验证手机号格式
    const phoneRegex = /^[1-9]\d{6,14}$/;
    for (let item of validNumbers) {
      if (!phoneRegex.test(item.number)) {
        notification.error({
          message: getIntl().formatMessage({
            id: 'marketing.test.phone.invalid',
            defaultMessage: '请输入有效的手机号格式',
          }),
        });
        return false;
      }
    }
    return true;
  };

  const handleSendTest = async () => {
    if (!validatePhoneNumbers()) return;

    try {
      const validNumbers = phoneNumbers.filter(
        item => item.number.trim() !== '',
      );
      const testData = validNumbers.map(item => item.countryCode + item.number);
      const [response] = await dispatchAllWithLoading([
        {
          type: 'marketingActivities/queryMarketingTestPhone',
          payload: {
            aiAgentId: aiAgentId,
            phoneNumberList: testData,
          },
        },
      ]);
      notification.success({
        message: getIntl().formatMessage({
          id: 'marketing.test.send.success',
          defaultMessage: '测试消息发送成功',
        }),
      });
    } catch (error) {
      console.log(error, 'error');
      notification.error({
        message: error?.message,
      });
    }
  };

  const handleCancelTest = () => {
    setWhatsappTestModal(false);
    // 重置为初始状态
    setPhoneNumbers([{ id: 1, countryCode: '+86', number: '' }]);
  };
  // 分享码
  const renderShareCodeContent = shareCode => {
    setFlowLoading2(true);
    try {
      dispatch({
        type: 'aiagent/getShareCodeContent',
        payload: { shareCode: shareCode },
        callback: response => {
          if (response.code === 200) {
            let data = response.data;
            let newNodes = data.nodesList.map(item => {
              item.data.setNodesDraggable = setNodesDraggable;
              item.data.nodesDraggable = nodesDraggable;
              return item;
            });
            setNodes(newNodes);
            setEdges(data.edgeList);
            setShareCodeModalVisible(false);
            setFlowLoading2(false);
          } else {
            notification.error({
              message: response.msg,
            });
            setFlowLoading2(false);
          }
        },
      });
    } catch (error) {
      notification.error({
        message: error?.message,
      });
      setFlowLoading2(false);
    }
  };

  const getTemplateUuidFromNodes = () => {
    let templateUuid = '';
    nodes.forEach(node => {
      if (
        node.data?.componentType === 'MarketingMessage' ||
        node.data?.componentType === 'MarketingMessage'
      ) {
        const form = node.data.customizForms?.find(
          item =>
            item.id === 'MarketingMessage' || item.id === 'MarketingMessage',
        );
        if (form && form.templateUuid) {
          templateUuid = form.templateUuid;
        }
      }
    });
    return templateUuid;
  };
  return (
    <Spin spinning={flowLoading || flowLoading2}>
      <div className={styles.canvasFlow} ref={drop} id="CanvasFlow">
        <div className={styles.canvasFlow_header}>
          <div className={styles.canvasFlow_header_left}>
            <span
              className={`${styles.handle}`}
              onClick={() => handleVerifySave()}
            >
              {BackIcon()}
            </span>
            {!agentNameEditing ? (
              <span style={{ display: 'flex', alignItems: 'center' }}>
                <span
                  className={styles.canvasFlow_header_name}
                  style={{ marginBottom: 5 }}
                >
                  {aiAgentInfo?.aiAgentName}
                </span>
                {![2, 3].includes(aiAgentInfo?.aiAgentType) ? (
                  <span
                    style={{ marginRight: 20 }}
                    className={`${styles.handle}`}
                    onClick={() => setAgentNameEditing(true)}
                  >
                    {EditGrayIcon()}
                  </span>
                ) : (
                  ''
                )}
              </span>
            ) : (
              <span style={{ display: 'flex', alignItems: 'center' }}>
                <span className={styles.canvasFlow_header_name}>
                  <Input
                    value={aiAgentNameTemp}
                    onChange={e => setAiAgentNameTemp(e.target.value)}
                    style={{ borderRadius: 6 }}
                  />
                </span>
                <span
                  style={{ marginRight: 20 }}
                  className={`${styles.handle}`}
                >
                  <span style={{ marginLeft: 10 }}>
                    <Button
                      icon={<CheckOutlined />}
                      onClick={() => updateAiAgentName()}
                      title={getIntl().formatMessage({
                        id: 'knowledge.QA.tabs.3.list.btn.tips4',
                        defaultValue: '保存',
                      })}
                      style={{ width: 28, height: 28 }}
                    ></Button>
                  </span>

                  <span style={{ marginLeft: 10 }}>
                    <Button
                      onClick={() => {
                        setAiAgentNameTemp(aiAgentInfo.aiAgentName);
                        setAgentNameEditing(false);
                      }}
                      icon={<CloseOutlined />}
                      title={getIntl().formatMessage({
                        id: 'knowledge.QA.tabs.3.list.btn.tips5',
                        defaultValue: '取消',
                      })}
                      style={{ width: 28, height: 28 }}
                    ></Button>
                  </span>
                </span>
              </span>
            )}
            <div className={styles.canvasFlow_header_channel}>
              {aiAgentInfo?.channelIds?.split(',')?.map(item => {
                switch (item) {
                  case '1':
                    return (
                      <img
                        src={NewEmailIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '3':
                    return (
                      <img
                        src={NewFaceBookIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '4':
                    return (
                      <img
                        src={NewWhatsAppIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '7':
                    return (
                      <img
                        src={NewPhoneIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '8':
                    return (
                      <img
                        src={NewWebChatIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '9':
                    return (
                      <img
                        src={NewAppChatIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '10':
                    return (
                      <img
                        src={NewWebOnlineVideoIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '11':
                    return (
                      <img
                        src={NewAppOnlineVideoIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '12':
                    return (
                      <img
                        src={NewAmazonMessageIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '13':
                    return (
                      <img
                        src={NewInstagramIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '14':
                    return (
                      <img
                        src={NewLineIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '15':
                    return (
                      <img
                        src={NewWeComIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '16':
                    return (
                      <img
                        src={NewWechatOfficialAccountIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '17':
                    return (
                      <img
                        src={NewWebOnlineVoiceIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '18':
                    return (
                      <img
                        src={NewAppOnlineVoiceIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '19':
                    return (
                      <img
                        src={NewTwitterIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '20':
                    return (
                      <img
                        src={NewTelegramIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '21':
                    return (
                      <img
                        src={NewWeChatMiniProgramIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '22':
                    return (
                      <img
                        src={NewShopifyIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '23':
                    return (
                      <img
                        src={NewGooglePlayIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                  case '25':
                    return (
                      <img
                        src={NewDiscordIcon}
                        style={{ width: 16, height: 16 }}
                      />
                    );
                }
              })}

              {![2, 3].includes(aiAgentInfo?.aiAgentType) ? (
                <span
                  style={{ height: 16 }}
                  className={`${styles.handle}`}
                  onClick={() => {
                    intelligentAgentRef.current?.resetFields();
                    setIsModalOpenChannel(true);
                  }}
                >
                  {EditGrayIcon()}
                </span>
              ) : (
                ''
              )}
            </div>
            <div
              className={styles.canvasFlow_header_shareCode}
              onClick={() => {
                setShareCodeModalVisible(true);
              }}
            >
              <ShareCodeIcon />
              <FormattedMessage
                id="ai.agent.nodes.header.shareCode"
                defaultValue="输入分享码"
              ></FormattedMessage>
            </div>
          </div>
          <div className={styles.canvasFlow_header_right}>
            {/* <div
              className={styles.canvasFlow_header_shortcut_tips}
              id="shortcut_tips"
            >
              <Tooltip
                placement="bottom"
                title={shortcutTipsRender()}
                color="#fff"
                getPopupContainer={() =>
                  document.getElementById('shortcut_tips')
                }
              >
                {shortcutTipsIcon()}
              </Tooltip>
            </div> */}
            <div className={styles.canvasFlow_header_tips}>
              {deployFlag ? (
                <Tooltip
                  placement="bottom"
                  title={getIntl().formatMessage({
                    id: 'ai.agent.nodes.header.tips.save',
                  })}
                >
                  {TipsSuccessIcon()}
                  <span
                    style={{ color: '#13C825', fontWeight: 600, marginLeft: 3 }}
                  >
                    <FormattedMessage
                      id="ai.agent.nodes.header.tips.status.3"
                      defaultValue="已部署"
                    ></FormattedMessage>
                  </span>
                </Tooltip>
              ) : (
                <Tooltip
                  placement="bottom"
                  title={getIntl().formatMessage({
                    id: 'ai.agent.nodes.header.tips.save',
                  })}
                >
                  {TipsIcon()}
                  <span
                    style={{ color: '#F22416', fontWeight: 600, marginLeft: 3 }}
                  >
                    <FormattedMessage
                      id="ai.agent.nodes.header.tips.status.1"
                      defaultValue="未部署"
                    ></FormattedMessage>
                  </span>
                </Tooltip>
              )}
            </div>
            <div className={styles.canvasFlow_header_operation}>
              {/* <span className={`${styles.handle}`}>{WithDrawIcon()}</span>
              <span className={`${styles.handle}`}>{ForWardIcon()}</span> */}
              <span className={`${styles.handle}`} onClick={() => copyNode()}>
                {NodeCopyIcon(16, 16)}
              </span>
              <span className={`${styles.handle}`} onClick={() => deleteNode()}>
                {NodeDeteleIcon(16, 16)}
              </span>
            </div>
            <Tooltip
              placement="bottom"
              title={getIntl().formatMessage({
                id: 'ai.agent.nodes.header.test.tips',
              })}
            >
              <Button
                onClick={() => {
                  setWhatsappTestModal(true);
                }}
                className={styles.canvasFlow_header_btnTest}
              >
                <FormattedMessage
                  id="ai.agent.nodes.header.test"
                  defaultValue="测试"
                ></FormattedMessage>
              </Button>
            </Tooltip>
            <Button
              className={styles.canvasFlow_header_save}
              onClick={() => AiAgentSave()}
              loading={loadingSave}
            >
              <FormattedMessage
                id="awsAccountSetting.save.btn"
                defaultValue="保存"
              ></FormattedMessage>
            </Button>
            <Dropdown
              menu={menuProps}
              className={styles.canvasFlow_header_deploy}
              overlayClassName={styles.canvasFlow_header_deploy_dropdown}
            >
              <Button
                onClick={() => AiAgentSaveAndDeploy()}
                loading={loadingSaveOrDeploy}
              >
                <Space>
                  <FormattedMessage
                    id="ai.agent.nodes.header.save"
                    defaultValue="保存并部署"
                  ></FormattedMessage>
                  <DownOutlined />
                </Space>
              </Button>
            </Dropdown>
          </div>
        </div>
        <ReactFlowProvider>
          <div style={{ height: '100%' }} ref={reactFlowWrapper}>
            <ReactFlow
              nodesDraggable={nodesDraggable}
              nodeTypes={nodeTypes}
              edgeTypes={edgeTypes}
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeClick={(e, value) => onNodeClick(e, value)}
              onNodeMouseEnter={(_, value) => onNodeMouseEnter(value)}
              onNodeMouseLeave={(_, value) => onNodeMouseLeave(value)}
              onConnectStart={(_, value) => onConnectStart(value)}
              onConnectStop={() => onConnectStop()}
              connectionLineComponent={ConnectionLine}
              // defaultEdgeOptions={defaultEdgeOptions}
              onSelectionChange={onSelectionChange}
              onInit={reactFlowInstance => {
                setReactFlowInstance(reactFlowInstance);
              }}
              panOnScroll={true}
              // onNodesDelete={onNodesDelete}
              // connectionMode={ConnectionMode.Loose}
            >
              {[2, 3].includes(aiAgentInfo?.aiAgentType) &&
              currentNodes?.data?.componentType === 'Start' ? (
                ''
              ) : currentNodes?.data?.isEdit ? (
                <CustomizePopup
                  node={currentNodes}
                  onSelectionChange={onSelectionChange}
                  setCurrentNodes={setCurrentNodes}
                  setIsModalOpenVariables={setIsModalOpenVariables}
                  setIsModalOpenApi={setIsModalOpenApi}
                ></CustomizePopup>
              ) : (
                ''
              )}
              <Controls showZoom={false}>
                <ControlButton
                  onClick={() => queryCurrentVarRef()}
                  className={styles.varControlsIcon}
                >
                  {/* <div
                style={{
                  background: isModalOpenVariables ? '#3463FC' : 'transparent',
                  borderRadius: 4,
                }}
              > */}
                  <VarControlsIcon />
                  {/* </div> */}
                </ControlButton>
                <ControlButton
                  onClick={() => setIsModalOpenApi(true)}
                  className={styles.varApiIcon}
                >
                  <VarApiIcon />
                </ControlButton>
                {/* <ControlButton
              onClick={() => alert('Something magical just happened. ✨')}
              className={styles.varHorizontalIcon}
            >
              <VarHorizontalIcon />
            </ControlButton>
            <ControlButton
              onClick={() => alert('Something magical just happened. ✨')}
              className={styles.varVerticalIcon}
            >
              <VarVerticalIcon />
            </ControlButton> */}
                {/* <ControlButton
                  onClick={() => setIsModalOpenSetting(true)}
                  className={styles.varSettingIcon}
                >
                  <VarSettingIcon />
                </ControlButton> */}
              </Controls>
              <MiniMap pannable zoomable />
              <Background gap={50} size={2} color="rgba(153, 153, 153, 0.2)" />
            </ReactFlow>
          </div>
        </ReactFlowProvider>
        <FormBox
          setComponentsList={setComponentsList}
          channelType="1"
          isMarketing={true}
        />
        <Modal
          title={getIntl().formatMessage({
            id: 'ai.agent.channnel',
            defaultValue: '渠道',
          })}
          open={isModalOpenChannel}
          footer={null}
          onCancel={() => {
            setIsModalOpenChannel(false);
          }}
          getContainer={() => document.getElementById('CanvasFlow')}
          mask={false}
          className={styles.canvasFlow_channelModal}
        >
          {/* <div className={styles.canvasFlow_modalContent}>
            <div className={styles.canvasFlow_modalContent_title}>
              <FormattedMessage
                id="ai.agent.channnel.options"
                defaultMessage="选择适用的渠道类型"
              />
            </div>
          </div> */}
          <Form
            name="basic"
            ref={intelligentAgentRef}
            onFinish={onFinish}
            autoComplete="off"
            layout="vertical"
            initialValues={{
              intelligentAgentChannelType: aiAgentInfo?.channelIds
                ?.split(',')
                ?.map(Number),
            }}
          >
            <Form.Item
              label={getIntl().formatMessage({
                id: 'external.intelligent.agent.create.select.channel.type',
                defaultValue: '选择适用的渠道类型',
              })}
              name="intelligentAgentChannelType"
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id:
                      'external.intelligent.agent.create.select.channel.type.tips',
                    defaultValue: '至少选择一个渠道类型',
                  }),
                },
              ]}
            >
              {aiAgentInfo?.channelTypeId === 1 ? (
                <Checkbox.Group>
                  {chatChannelList?.map(chatChannelItem => (
                    <Checkbox value={chatChannelItem.key}>
                      <div className="channelContainer">
                        <img src={chatChannelItem.channelIcon} />
                        <p>{chatChannelItem.channelName}</p>
                      </div>
                    </Checkbox>
                  ))}
                </Checkbox.Group>
              ) : aiAgentInfo?.channelTypeId === 2 ? (
                <Checkbox.Group>
                  <Checkbox value={1}>
                    <div className="channelContainer">
                      <img src={NewEmailIcon} />
                      <p>
                        <FormattedMessage
                          id="marketing.channel.type.email"
                          defaultMessage="邮件"
                        />
                      </p>
                    </div>
                  </Checkbox>
                </Checkbox.Group>
              ) : (
                <Checkbox.Group>
                  {phoneChannelList?.map(channelItem => (
                    <Checkbox value={channelItem.key}>
                      <div className="channelContainer">
                        <img src={channelItem.channelIcon} />
                        <p>{channelItem.channelName}</p>
                      </div>
                    </Checkbox>
                  ))}
                </Checkbox.Group>
              )}
            </Form.Item>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  style={{ textAlign: 'center' }}
                  className={styles.canvasFlow_modalFoot}
                >
                  <Button onClick={() => setIsModalOpenChannel(false)}>
                    <FormattedMessage
                      id="awsAccountSetting.cancel.btn"
                      defaultMessage="取消"
                    />
                  </Button>
                  <Button
                    type={'primary'}
                    htmlType="submit"
                    loading={loadingBtn}
                  >
                    <FormattedMessage
                      id="work.order.saved.btn"
                      defaultMessage="保存"
                    />
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
        <Modal
          title={getIntl().formatMessage({
            id: 'ai.agent.back.title',
            defaultValue: '未保存提示',
          })}
          open={isModalOpenBack}
          footer={null}
          onCancel={() => {
            setIsModalOpenBack(false);
          }}
          getContainer={() => document.getElementById('CanvasFlow')}
          mask={false}
          className={styles.canvasFlow_backModal}
        >
          <div className={styles.canvasFlow_backContent}>
            <div className={styles.canvasFlow_backContent_title}>
              <span style={{ marginRight: 5 }}>{TipsBackIcon()}</span>
              <FormattedMessage
                id="ai.agent.back.title.content"
                defaultMessage="当前智能体已修改，请问是否要保存修改？"
              />
            </div>
          </div>
          <div className={styles.canvasFlow_modalFoot}>
            <Button
              onClick={() => {
                setIsModalOpenBack(false);
              }}
            >
              <FormattedMessage
                id="awsAccountSetting.cancel.btn"
                defaultMessage="取消"
              />
            </Button>
            <Button onClick={() => goBack()}>
              <FormattedMessage
                id="ai.agent.back.button.no"
                defaultMessage="不保存"
              />
            </Button>

            <Button
              onClick={() => AiAgentSave('back')}
              type={'primary'}
              loading={loadingSave}
            >
              <FormattedMessage
                id="work.order.saved.btn"
                defaultMessage="保存"
              />
            </Button>
          </div>
        </Modal>
        <Modal
          title={getIntl().formatMessage({
            id: 'ai.agent.var',
            defaultValue: '变量管理',
          })}
          open={isModalOpenVariables}
          footer={null}
          onCancel={() => {
            setIsModalOpenVariables(false);
          }}
          getContainer={() => document.getElementById('CanvasFlow')}
          mask={false}
          className={styles.canvasFlow_variablesModal}
        >
          <div className={styles.canvasFlow_variablesModal_content}>
            <div className={styles.canvasFlow_variablesModal_form}>
              <div className={styles.canvasFlow_variablesModal_form_left}>
                <Input
                  placeholder={getIntl().formatMessage({
                    id: 'ai.agent.nodes.form.search',
                  })}
                  value={variableName}
                  prefix={<Search />}
                  onChange={e => setVariableName(e.target.value)}
                  onPressEnter={() => onPressEnter()}
                />
                {/* <Select
                onChange={e => setLabelValue(e.target.value)}
                placeholder={getIntl().formatMessage({
                  id: 'ai.agent.var.select',
                  defaultValue: '选择类型',
                })}
              ></Select> */}
              </div>
              {getCurrentKeyValue ? (
                <div className={styles.canvasFlow_variablesModal_form_right}>
                  <Button
                    onClick={() => handleAddVarItRef()}
                    type={'primary'}
                    icon={<PlusOutlined />}
                  >
                    <FormattedMessage
                      id="ai.agent.var.add"
                      defaultMessage="添加变量"
                    />
                  </Button>
                </div>
              ) : (
                ''
              )}
            </div>

            <TableVar
              ref={tableVarRef}
              aiAgentId={aiAgentId}
              variableName={variableName}
              getCurrentKey={getCurrentKey}
            ></TableVar>
          </div>
        </Modal>
        <Modal
          title={getIntl().formatMessage({
            id: 'connect.beginner.guide.title.2',
            defaultValue: '设置',
          })}
          open={isModalOpenSetting}
          footer={null}
          onCancel={() => {
            waitingReplyTableRef.current?.handleCloseModal();
            setIsModalOpenSetting(false);
          }}
          getContainer={() => document.getElementById('CanvasFlow')}
          mask={false}
          className={styles.canvasFlow_settingModal}
        >
          <Spin spinning={loadingSetting}>
            <div className={styles.canvasFlow_settingModal_content}>
              <div className={styles.canvasFlow_settingModal_title}>
                <FormattedMessage
                  id="ai.agent.var.setting.content.title"
                  defaultMessage="客户不回复消息时的主动提醒"
                />
              </div>
              <div className={styles.canvasFlow_settingModal_tip}>
                <FormattedMessage
                  id="ai.agent.var.setting.content.tip"
                  defaultMessage="此处设置是机器人在遇到客户不回复消息时的主动提醒"
                />
              </div>
              <div className={styles.canvasFlow_settingModal_table}>
                <WaitingReplyTable
                  ref={waitingReplyTableRef}
                  aiAgentId={aiAgentId}
                  handleChangeLoading={handleChangeLoading}
                  waitSettingList={waitSettingList}
                  queryReminderList={queryReminderList}
                />
              </div>
            </div>
          </Spin>
        </Modal>
        <Modal
          title={getIntl().formatMessage({
            id: 'ai.agent.api.modal.title',
            defaultValue: 'API管理',
          })}
          open={isModalOpenApi}
          onCancel={() => setIsModalOpenApi(false)}
          footer={null}
          getContainer={() => document.getElementById('CanvasFlow')}
          mask={false}
          className={styles.canvasFlow_apiManageModal}
          destroyOnClose={true}
        >
          <ApiManage />
        </Modal>

        {/* WhatsApp营销测试模态框 */}
        <Modal
          title={
            <div className={styles.whatsapp_test_modal_header}>
              <div className={styles.whatsapp_test_modal_title}>
                <FormattedMessage
                  id="marketing.test.modal.title"
                  defaultMessage="WhatsApp营销测试"
                />
              </div>
              <div
                className={styles.whatsapp_test_modal_close}
                onClick={handleCancelTest}
              >
                <CloseOutlined />
              </div>
            </div>
          }
          open={whatsappTestModal}
          onCancel={handleCancelTest}
          footer={null}
          getContainer={() => document.getElementById('CanvasFlow')}
          mask={true}
          className={styles.whatsapp_test_modal}
          destroyOnClose={true}
          width={348}
          closable={false}
          destroyOnHidden={true}
        >
          <div className={styles.whatsapp_test_modal_content}>
            <div className={styles.whatsapp_test_modal_form}>
              <div className={styles.phone_input_label}>
                <FormattedMessage
                  id="marketing.test.phone.label"
                  defaultMessage="请输入测试的手机号（最多5个）"
                />
              </div>

              {phoneNumbers.map((phone, index) => (
                <div key={phone.id} className={styles.phone_input_row}>
                  <div className={styles.phone_input_country}>
                    <Select
                      value={phone.countryCode}
                      onChange={value =>
                        handlePhoneNumberChange(phone.id, 'countryCode', value)
                      }
                      style={{ width: 82 }}
                      options={areaNumList.map(item => ({
                        value: item.code,
                        label: item.code,
                      }))}
                    />
                  </div>
                  <div className={styles.phone_input_number}>
                    <Input
                      placeholder={getIntl().formatMessage({
                        id: 'marketing.test.phone.placeholder',
                        defaultMessage: '请输入手机号',
                      })}
                      value={phone.number}
                      onChange={e =>
                        handlePhoneNumberChange(
                          phone.id,
                          'number',
                          e.target.value,
                        )
                      }
                      maxLength={15}
                    />
                  </div>
                  <div className={styles.phone_input_action}>
                    {index === 0 && phoneNumbers.length < 5 ? (
                      <div
                        onClick={addPhoneNumber}
                        style={{
                          width: 16,
                          height: 16,
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <WhatsappTestModalAddIcon />
                      </div>
                    ) : index === 0 ? (
                      <div style={{ width: 16, height: 16 }} />
                    ) : (
                      <div
                        onClick={() => removePhoneNumber(phone.id)}
                        style={{
                          width: 16,
                          height: 16,
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <WhatsappTestModalCloseIcon />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className={styles.whatsapp_test_modal_footer}>
              <Button
                onClick={handleCancelTest}
                className={styles.cancel_button}
              >
                <FormattedMessage
                  id="marketing.test.cancel"
                  defaultMessage="取消"
                />
              </Button>
              <Button
                type="primary"
                onClick={handleSendTest}
                loading={testLoading}
                className={styles.send_button}
              >
                <FormattedMessage
                  id="marketing.test.send"
                  defaultMessage="发送"
                />
              </Button>
            </div>
          </div>
        </Modal>
      </div>
      <ShareCodeModal
        visible={shareCodeModalVisible}
        onClose={() => {
          setShareCodeModalVisible(false);
        }}
        mode="edit"
        title={getIntl().formatMessage({
          id: 'shareComponents.modal.title',
        })}
        subTitle={getIntl().formatMessage({
          id: 'shareComponents.modal.subTitle',
        })}
        note={getIntl().formatMessage({
          id: 'external.intelligent.agent.share.tips.edit',
        })}
        isEdit={true}
        onConfirm={shareCode => renderShareCodeContent(shareCode)}
      />
    </Spin>
  );
};
const TableVar = forwardRef(
  ({ aiAgentId, variableName, getCurrentKey }, ref) => {
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const [editingKey, setEditingKey] = useState('');
    const [currentTabKey, setCurrentTabKey] = useState(3);
    //变量弹窗相关数据
    const [rowsVar3, setRowsVar3] = useState([]);
    const [rowsVar2, setRowsVar2] = useState([]);
    const [rowsVar4, setRowsVar4] = useState([]);
    const [rowsVar1, setRowsVar1] = useState([]);
    const [tabsItems, setTabsItems] = useState([]);
    const [loadingVar, setLoadingVar] = useState(false);
    //编辑
    const setEditingTable = (value, data) => {
      form.setFieldsValue({
        variableName: '',
        dataType: '',
        defaultValue: '',
        ...data,
      });

      setEditingKey(data.variableId);
    };
    //取消
    const cencelUpdate = (value, data) => {
      if (data.variableId !== '') {
        setEditingKey('');
      } else {
        if (currentTabKey === 3) {
          let arr = [...rowsVar3];
          arr.pop();
          setRowsVar3(arr);
        } else if (currentTabKey === 2) {
          let arr = [...rowsVar2];
          arr.pop();
          setRowsVar2(arr);
        } else if (currentTabKey === 4) {
          let arr = [...rowsVar4];
          arr.pop();
          setRowsVar4(arr);
        } else if (currentTabKey === 1) {
          let arr = [...rowsVar1];
          arr.pop();
          setRowsVar1(arr);
        }
      }
    };
    //检验变量名称不能为中文，长度200
    const validateInput = input => {
      const regex = /^(?!.*[\u4e00-\u9fa5])[^\u4e00-\u9fa5]{0,200}$/;
      return regex.test(input);
    };
    const saveUpdate = async (value, data) => {
      let newData = [];
      if (currentTabKey === 3) {
        newData = [...rowsVar3];
      } else if (currentTabKey === 2) {
        newData = [...rowsVar2];
      } else if (currentTabKey === 4) {
        newData = [...rowsVar4];
      } else if (currentTabKey === 1) {
        newData = [...rowsVar1];
      }
      try {
        const row = await form.validateFields();
        const obj = {
          variableType: currentTabKey,
          required: 0, //是否必填（0非必须，1必须）
          aiAgentId: aiAgentId, //智能体ID
          variableId: data.variableId,
          ...row,
        };
        if (!validateInput(obj.variableName)) {
          notification.error({
            message: getIntl().formatMessage({
              id: 'ai.agent.var.name.rules',
            }),
          });
        } else {
          if (obj.variableId === '') {
            addCurrentVar(obj);
          } else {
            updateCurrentVar(obj);
            setEditingKey('');
          }
        }
      } catch (errInfo) {
        console.log('Validate Failed:', errInfo);
      }
    };
    const addCurrentVar = row => {
      setLoadingVar(true);
      dispatch({
        type: 'aiagent/addCurrentVar',
        payload: row,
        callback: response => {
          let { code, data, msg } = response;
          if (200 === code) {
            queryCurrentVar();
          } else {
            setLoadingVar(false);
            notification.error({
              message: msg,
            });
          }
        },
      });
    };
    const updateCurrentVar = row => {
      setLoadingVar(true);
      dispatch({
        type: 'aiagent/updateCurrentVar',
        payload: {
          id: row.variableId,
          data: row,
        },
        callback: response => {
          let { code, data, msg } = response;
          if (200 === code) {
            queryCurrentVar();
          } else {
            setLoadingVar(false);

            notification.error({
              message: msg,
            });
          }
        },
      });
    };
    const deteleCurrentVar = (_, data) => {
      setLoadingVar(true);
      dispatch({
        type: 'aiagent/deteleCurrentVar',
        payload: {
          id: data.variableId,
        },
        callback: response => {
          let { code, data, msg } = response;
          if (200 === code) {
            queryCurrentVar();
          } else {
            setLoadingVar(false);

            notification.error({
              message: msg,
            });
          }
        },
      });
    };
    const isEditing = record => record.variableId === editingKey;
    // table 表头
    const columnsVar = [
      {
        title: getIntl().formatMessage({
          id: 'ai.agent.var.table.1',
        }),
        dataIndex: 'variableName',
        key: 'variableName',
        ellipsis: true,
        align: 'center',
        editable: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'ai.agent.var.table.2',
        }),
        dataIndex: 'dataType',
        key: 'dataType',
        ellipsis: true,
        align: 'center',
        editable: true,
        render: (value, data) => {
          return data.dataType === 'string' ? (
            <span>
              {getIntl().formatMessage({
                id: 'ai.agent.var.table.select.option.1',
              })}
            </span>
          ) : data.dataType === 'Long' ? (
            <span>
              {getIntl().formatMessage({
                id: 'ai.agent.var.table.select.option.2',
              })}
            </span>
          ) : data.dataType === 'LIST' ? (
            <span>
              {getIntl().formatMessage({
                id: 'ai.agent.var.table.select.option.4',
              })}
            </span>
          ) : (
            <span>
              {getIntl().formatMessage({
                id: 'ai.agent.var.table.select.option.3',
              })}
            </span>
          );
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'ai.agent.var.table.3',
        }),
        dataIndex: 'defaultValue',
        key: 'defaultValue',
        ellipsis: true,
        align: 'center',
        editable: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'message.operation',
        }),
        width: 100,
        key: 'operation',
        fixed: 'right',
        ellipsis: true,
        render: (value, data) => {
          const editable = isEditing(data);
          return value.variableType === 1 ? null : !editable ? (
            <>
              <div className={styles.operationArray}>
                <span style={{ marginLeft: 10 }}>
                  <Button
                    onClick={() => setEditingTable(value, data)}
                    icon={<AIGCEdit />}
                    title={getIntl().formatMessage({
                      id: 'knowledge.QA.tabs.3.list.btn.tips2',
                      defaultValue: '编辑',
                    })}
                  ></Button>
                </span>
                <Popconfirm
                  title={getIntl().formatMessage({
                    id:
                      'document.knowledge.base.table.operation.delete.Popconfirm',
                    defaultValue: '确定删除该条数据吗？',
                  })}
                  onConfirm={() => deteleCurrentVar(value, data)}
                >
                  <span
                    style={{ marginLeft: 10 }}
                    className={styles.deleteClass}
                  >
                    <Button
                      // onClick={() => deteleCurrentVar(value, data)}
                      icon={<DeleteHome style={{ color: '#3463FC' }} />}
                      title={getIntl().formatMessage({
                        id: 'knowledge.QA.tabs.3.list.btn.tips3',
                        defaultValue: '删除',
                      })}
                    ></Button>
                  </span>
                </Popconfirm>
              </div>
            </>
          ) : (
            <>
              <div className={styles.operationArray}>
                <span style={{ marginLeft: 10 }}>
                  <Button
                    // onClick={() => this.updateReply(value, data)}
                    icon={<CheckOutlined />}
                    onClick={() => saveUpdate(value, data)}
                    title={getIntl().formatMessage({
                      id: 'knowledge.QA.tabs.3.list.btn.tips4',
                      defaultValue: '保存',
                    })}
                  ></Button>
                </span>

                <span style={{ marginLeft: 10 }}>
                  <Button
                    onClick={() => cencelUpdate(value, data)}
                    icon={<CloseOutlined />}
                    title={getIntl().formatMessage({
                      id: 'knowledge.QA.tabs.3.list.btn.tips5',
                      defaultValue: '取消',
                    })}
                  ></Button>
                </span>
              </div>
            </>
          );
        },
      },
    ];
    const mergedColumns = columnsVar.map(col => {
      if (!col.editable) {
        return col;
      }
      return {
        ...col,
        onCell: record => ({
          record,
          inputType: col.dataIndex === 'dataType' ? 'Select' : 'Input',
          dataIndex: col.dataIndex,
          title: col.title,
          editing: isEditing(record),
        }),
      };
    });
    //动态表单表格
    const EditableCell = ({
      editing,
      dataIndex,
      title,
      inputType,
      require,
      record,
      index,
      children,
      ...restProps
    }) => {
      console.log(
        editing,
        dataIndex,
        title,
        inputType,
        require,
        record,
        index,
        children,
        'EditableCell',
      );
      const inputNode =
        inputType === 'Input' ? (
          <Input className={styles.varInput} defaultValue={children} />
        ) : (
          <Select
            options={[
              {
                value: 'string',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.table.select.option.1',
                }),
              },
              {
                value: 'Long',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.table.select.option.2',
                }),
              },
              {
                value: 'JSON',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.table.select.option.3',
                }),
              },
              {
                value: 'LIST',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.table.select.option.4',
                }),
              },
            ]}
          ></Select>
        );
      return (
        <td {...restProps}>
          {editing ? (
            <Form.Item
              name={dataIndex}
              style={{
                margin: 0,
              }}
              rules={[
                {
                  required: record.required === 0 ? false : true,
                  message: `Please Input ${title}!`,
                },
                {
                  max: 200,
                  message: (
                    <FormattedMessage
                      id="new.intent.intention.attribute.verify.rule.maxlength"
                      defaultValue="长度不能超过200个字符"
                    />
                  ),
                },
              ]}
            >
              {inputNode}
            </Form.Item>
          ) : (
            children
          )}
        </td>
      );
    };
    //当前智能体变量表格
    const queryCurrentVar = () => {
      setLoadingVar(true);
      dispatch({
        type: 'aiagent/queryCurrentVar',
        payload: {
          id: aiAgentId || localStorage.getItem('aiAgentId'), //智能体ID
          variableName: variableName,
        },
        callback: response => {
          setLoadingVar(false);
          let { code, data, msg } = response;
          if (200 === code) {
            let arr3 = [];
            let arr2 = [];
            let arr4 = [];
            let arr1 = [];
            //变量类型（1.系统内置变量，2当前会话变量，3当前智能体变量，4全局变量）
            data?.forEach(item => {
              if (item.variableType === 3) {
                arr3.push(item);
              } else if (item.variableType === 2) {
                arr2.push(item);
              } else if (item.variableType === 4) {
                arr4.push(item);
              } else if (item.variableType === 1) {
                arr1.push(item);
              }
            });
            setRowsVar3(arr3);
            setRowsVar2(arr2);
            setRowsVar4(arr4);
            setRowsVar1(arr1);
            setTabsItems([
              {
                label: (
                  <div
                    style={{
                      display: 'flex',
                    }}
                  >
                    <span>
                      {getIntl().formatMessage({
                        id: 'ai.agent.var.tabs.1',
                        defaultMessage: '当前智能体变量',
                      })}
                    </span>
                    <span className={styles.numBox}>{arr3.length}</span>
                  </div>
                ),
                key: 3,
                children: '',
              },
              {
                label: (
                  <div
                    style={{
                      display: 'flex',
                    }}
                  >
                    <span>
                      {getIntl().formatMessage({
                        id: 'ai.agent.var.tabs.2',
                        defaultMessage: '当前会话变量',
                      })}
                    </span>
                    <span className={styles.numBox}>{arr2.length}</span>
                  </div>
                ),
                key: 2,
                children: '',
              },
              {
                label: (
                  <div
                    style={{
                      display: 'flex',
                    }}
                  >
                    <span>
                      {getIntl().formatMessage({
                        id: 'ai.agent.var.tabs.3',
                        defaultMessage: '全局变量',
                      })}
                    </span>
                    <span className={styles.numBox}>{arr4.length}</span>
                  </div>
                ),
                key: 4,
                children: '',
              },
              {
                label: (
                  <div
                    style={{
                      display: 'flex',
                    }}
                  >
                    <span>
                      {getIntl().formatMessage({
                        id: 'ai.agent.var.tabs.4',
                        defaultMessage: '系统内置变量',
                      })}
                    </span>
                    <span className={styles.numBox}>{arr1.length}</span>
                  </div>
                ),

                key: 1,
                children: '',
              },
            ]);
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    };
    //添加变量
    const handleAddVarIt = () => {
      let obj = {
        variableId: '',
        variableName: '',
        dataType: '',
        defaultValue: '',
        variableType: currentTabKey,
        required: 0, //是否必填（0非必须，1必须）
        aiAgentId: aiAgentId, //智能体ID
      };

      //注意需要校验只允许存在一个表单，因为每行表单公用同一个key
      if (currentTabKey === 3) {
        if (
          rowsVar3.findIndex(item => item.variableId === '') === -1 &&
          editingKey === ''
        ) {
          //清空表单
          form.setFieldsValue({
            variableName: '',
            dataType: '',
            defaultValue: '',
          });
          let arr = [...rowsVar3, obj];
          setRowsVar3(arr);
        } else {
          notification.error({
            message: getIntl().formatMessage({
              id: 'ai.agent.nodes.startNode.var.table.add.warn',
            }),
          });
        }
      } else if (currentTabKey === 2) {
        if (
          rowsVar2.findIndex(item => item.variableId === '') === -1 &&
          editingKey === ''
        ) {
          //清空表单
          form.setFieldsValue({
            variableName: '',
            dataType: '',
            defaultValue: '',
          });
          let arr = [...rowsVar2, obj];
          setRowsVar2(arr);
        } else {
          notification.error({
            message: getIntl().formatMessage({
              id: 'ai.agent.nodes.startNode.var.table.add.warn',
            }),
          });
        }
      } else if (currentTabKey === 4) {
        if (
          rowsVar4.findIndex(item => item.variableId === '') === -1 &&
          editingKey === ''
        ) {
          //清空表单
          form.setFieldsValue({
            variableName: '',
            dataType: '',
            defaultValue: '',
          });
          let arr = [...rowsVar4, obj];
          setRowsVar4(arr);
        } else {
          notification.error({
            message: getIntl().formatMessage({
              id: 'ai.agent.nodes.startNode.var.table.add.warn',
            }),
          });
        }
      } else if (currentTabKey === 1) {
        if (
          rowsVar1.findIndex(item => item.variableId === '') === -1 &&
          editingKey === ''
        ) {
          //清空表单
          form.setFieldsValue({
            variableName: '',
            dataType: '',
            defaultValue: '',
          });
          let arr = [...rowsVar1, obj];
          setRowsVar1(arr);
        } else {
          notification.error({
            message: getIntl().formatMessage({
              id: 'ai.agent.nodes.startNode.var.table.add.warn',
            }),
          });
        }
      }
    };

    useImperativeHandle(ref, () => ({
      queryCurrentVar,
      handleAddVarIt,
    }));

    return (
      <Form form={form} component={false}>
        <Tabs
          defaultActiveKey={currentTabKey}
          type="card"
          items={tabsItems}
          onChange={e => {
            setCurrentTabKey(e);
            getCurrentKey(e);
          }}
        />
        <Table
          components={{
            body: {
              cell: EditableCell,
            },
          }}
          loading={loadingVar}
          columns={mergedColumns}
          dataSource={
            currentTabKey === 3
              ? rowsVar3
              : currentTabKey === 2
              ? rowsVar2
              : currentTabKey === 4
              ? rowsVar4
              : rowsVar1
          }
          scroll={{
            x: 'calc(51vw - 30px)',
            y: 'calc(58vh - 250px)',
          }}
          pagination={false}
        />
      </Form>
    );
  },
);

// 设置等待回复可编辑表格
const WaitingReplyTable = forwardRef((props, ref) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState('');
  const isEditing = record => record.key === editingKey;
  useEffect(() => {
    const transformedData = props.waitSettingList?.map(item => ({
      key: item.reminderId, // 添加 key 属性
      waitingTime:
        item.waitMinutes +
        getIntl().formatMessage({
          id: 'ai.agent.script.time.minute',
          defaultValue: '分',
        }) +
        item.waitSeconds +
        getIntl().formatMessage({
          id: 'ai.agent.script.time.second',
          defaultValue: '秒',
        }),
      ...item, // 保留原始数据
    }));
    setData(transformedData);
  }, [props.waitSettingList]);
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === 'number' ? (
        <InputNumber
          precision={0}
          min={0}
          max={60}
          disabled={editing ? false : true}
          placeholder={getIntl().formatMessage({
            id: 'work.order.management.tips',
            defaultValue: '请输入',
          })}
        />
      ) : (
        <TextArea
          max={2001}
          disabled={editing ? false : true}
          autoSize={{
            minRows: 2,
            maxRows: 2,
          }}
          placeholder={getIntl().formatMessage({
            id:
              'new.intent.intention.rhetorical.question.not.collecting.placeholder',
            defaultValue: '请输入话术',
          })}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <>
            {dataIndex === 'reminderText' ? (
              <Form.Item
                name={dataIndex}
                rules={[
                  {
                    required: true,
                    message: getIntl().formatMessage({
                      id:
                        'new.intent.intention.rhetorical.question.not.collecting.placeholder',
                      defaultValue: '请输入话术',
                    }),
                  },
                  {
                    max: 2000,
                    message: (
                      <FormattedMessage
                        id="new.intent.intention.reason.maxlength"
                        defaultValue="长度不能超过2000个字符"
                      />
                    ),
                  },
                ]}
              >
                {inputNode}
              </Form.Item>
            ) : (
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    name="waitMinutes"
                    rules={[
                      {
                        required: true,
                        message: getIntl().formatMessage({
                          id: 'work.order.management.tips',
                          defaultValue: '请输入',
                        }),
                      },
                      {
                        type: 'number',
                        max: 59,
                        message: getIntl().formatMessage({
                          id: 'ai.agent.script.time.tips',
                          defaultValue: '请输入0~59之前的数字',
                        }),
                      },
                    ]}
                  >
                    {inputNode}
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <FormattedMessage
                    id="ai.agent.script.time.minute"
                    defaultMessage="分"
                  />
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="waitSeconds"
                    rules={[
                      {
                        required: true,
                        message: getIntl().formatMessage({
                          id: 'work.order.management.tips',
                          defaultValue: '请输入',
                        }),
                      },
                      {
                        type: 'number',
                        max: 59,
                        message: getIntl().formatMessage({
                          id: 'ai.agent.script.time.tips',
                          defaultValue: '请输入0~59之前的数字',
                        }),
                      },
                    ]}
                  >
                    {inputNode}
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <FormattedMessage
                    id="ai.agent.script.time.second"
                    defaultMessage="秒"
                  />
                </Col>
              </Row>
            )}
          </>
        ) : (
          children
        )}
      </td>
    );
  };
  // 添加提醒话术
  const handleAddSetting = () => {
    if (data.length < 5) {
      const newData = {
        key: 'defaultKey',
        waitingTime: '',
        reminderText: '',
      };
      setEditingKey('defaultKey');
      setData([newData, ...data]);
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'ai.agent.script.settings.num.tips',
          defaultValue: '提醒设置不能超过 5 个',
        }),
      });
    }
  };

  const edit = record => {
    form.setFieldsValue({
      ...record,
    });
    setEditingKey(record.key);
  };
  const cancel = key => {
    if (key === 'defaultKey') {
      form.resetFields(); // 清空表单
      setEditingKey('');
      const newData = data.filter(item => item.key !== key);
      setData(newData);
      props.queryReminderList();

      setEditingKey('');
    } else {
      setData([]);
      setEditingKey('');
      form.resetFields(); // 清空表单
      props.queryReminderList();
    }
  };
  // 保存新建等待回复提醒
  const save = async record => {
    try {
      props.handleChangeLoading(true);
      const row = await form.validateFields();
      // 判断分秒最少需要1秒
      let totalSecond = row.waitMinutes * 60 + row.waitSeconds;
      if (totalSecond >= 1 && row.reminderText) {
        let params;
        if (record.key === 'defaultKey') {
          // 新增
          params = {
            reminderId: '',
            aiAgentId: props.aiAgentId, //智能体ID
            waitMinutes: row.waitMinutes,
            waitSeconds: row.waitSeconds,
            reminderText: row.reminderText,
          };
        } else {
          // 修改
          params = {
            reminderId: record.reminderId,
            aiAgentId: record.aiAgentId, //智能体ID
            waitMinutes: row.waitMinutes,
            waitSeconds: row.waitSeconds,
            reminderText: row.reminderText,
          };
        }
        dispatch({
          type: 'aiagent/saveReminderList',
          payload: params,
          callback: response => {
            let { code, data, msg } = response;
            if (200 === code) {
              setData([]);
              setEditingKey('');
              form.resetFields(); // 清空表单
              props.queryReminderList();
            } else {
              props.handleChangeLoading(false);
              notification.error({
                message: msg,
              });
            }
          },
        });
      } else {
        props.handleChangeLoading(false);
        notification.warning({
          message: getIntl().formatMessage({
            id: 'ai.agent.script.settings.not.empty.tips',
            defaultValue: '等待时间和提醒话术不能为空！',
          }),
        });
      }
    } catch (errInfo) {
      console.log('Validate Failed:', errInfo);
    }
  };

  // 删除提醒数据
  const deleteScript = record => {
    props.handleChangeLoading(true);
    dispatch({
      type: 'aiagent/deleteReminderList',
      payload: record.reminderId,
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          notification.success({
            message: msg,
          });
          props.queryReminderList();
        } else {
          props.handleChangeLoading(false);
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  const columns = [
    {
      title: getIntl().formatMessage({
        id: 'ai.agent.waiting.reply.table.waiting.time',
        defaultValue: '等待时间',
      }),
      dataIndex: 'waitingTime',
      width: '35%',
      editable: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'ai.agent.waiting.reply.table.reminder.language',
        defaultValue: '提醒话术',
      }),
      dataIndex: 'reminderText',
      width: '50%',
      editable: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'customerInformation.table.operation',
        defaultValue: '操作',
      }),
      dataIndex: 'operation',
      width: '15%',
      fixed: 'right',
      ellipsis: true,
      render: (_, record) => {
        const editable = isEditing(record);
        return editable ? (
          <span>
            <Typography.Link onClick={() => save(record)}>
              <img src={SaveReplyIcon} />
            </Typography.Link>
            <Popconfirm
              title={getIntl().formatMessage({
                id: 'new.intent.add.cancel.confirm',
                defaultValue: '取消将清空表单，确定取消么？',
              })}
              onConfirm={() => cancel(record.key)}
            >
              <img src={CancelSaveReplyIcon} />
            </Popconfirm>
          </span>
        ) : (
          <span>
            <Typography.Link
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
            >
              <img src={EditorReplyIcon} />
            </Typography.Link>
            <Popconfirm
              disabled={editingKey !== ''}
              title={getIntl().formatMessage({
                id: 'document.knowledge.base.table.operation.delete.Popconfirm',
                defaultValue: '确定删除该条数据吗？',
              })}
              onConfirm={() => deleteScript(record)}
            >
              <img src={DeleteReplyIcon} />
            </Popconfirm>
          </span>
        );
      },
    },
  ];
  const mergedColumns = columns.map(col => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: record => ({
        record,
        inputType: col.dataIndex === 'waitingTime' ? 'number' : 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });
  // 关闭弹窗清空数据
  const handleCloseModal = () => {
    form.resetFields(); // 清空表单
    setEditingKey('');
    props.queryReminderList();
  };
  useImperativeHandle(ref, () => ({
    handleCloseModal,
  }));
  return (
    <div>
      <div className={styles.canvasFlow_settingModal_add}>
        <Button
          disabled={editingKey ? true : false}
          onClick={() => handleAddSetting()}
          type={'primary'}
          icon={<PlusOutlined />}
        >
          <FormattedMessage
            id="ai.agent.script.add"
            defaultMessage="添加话术"
          />
        </Button>
      </div>
      <Form form={form} component={false}>
        <Table
          components={{
            body: {
              cell: EditableCell,
            },
          }}
          bordered
          dataSource={data}
          columns={mergedColumns}
          rowClassName="waiting-reply-editable-row"
          scroll={{ y: 360 }}
          pagination={false}
        />
      </Form>
    </div>
  );
});

// API管理
const ApiManage = forwardRef((props, ref) => {
  const [activeApiTabIndex, setActiveApiTabIndex] = useState(0);
  // 0: params, 1: headers, 2: body, 3: Authentication
  const [activeApiTestBodyTab, setActiveApiTestBodyTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const apiMethodOptions = [
    { value: 'GET', label: 'GET' },
    { value: 'POST', label: 'POST' },
    { value: 'PUT', label: 'PUT' },
    { value: 'DELETE', label: 'DELETE' },
  ];
  const [apiList, setApiList] = useState([]);
  const dispatch = useDispatch();
  useEffect(() => {
    getApiList();
  }, []);

  // 获取API列表
  const getApiList = (value = '') => {
    if (!loading) setLoading(true);
    dispatch({
      type: 'apiManage/getApiList',
      payload: {
        apiName: value,
      },
      callback: response => {
        if (response.code === 200) {
          response.data.forEach(item => {
            item.isEditing = false;
            item.editApiText = '';
            item.name = item.apiName;
            item.method = item.apiMethod;
            item.url = item.apiPath;
            item.params = JSON.parse(item.params);
            item.headers = JSON.parse(item.header);
            item.body = JSON.parse(item.body);
            item.activeBodyType = item.bodyType === 2 ? 'form-data' : 'raw';
            item.activeTab = 'params';
            item.response = {
              body: JSON.stringify(),
            };
            item.authentication = item.authorization
              ? JSON.parse(item.authorization)
              : {
                  isAuthenticated: false,
                  authType: '1',
                  apiKey: '',
                  header: '',
                };
          });
          console.log(response.data);
          setApiList(response.data);
          setLoading(false);
        } else {
          setLoading(false);
          notification.error({
            message: response.msg,
          });
        }
      },
      error: response => {
        setLoading(false);
      },
    });
  };
  // 切换tab
  const handleTabChange = index => {
    const text = apiList[index].body.raw.content;
    console.log('text', text);
    setActiveApiTabIndex(index);
  };
  const [searchApiText, setSearchApiText] = useState('');
  // 搜索API
  const searchApi = value => {
    getApiList(searchApiText);
  };
  // 编辑API名称
  const editApiTextChange = (value, index) => {
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      newList[index].name = value;
      newList[index].editApiText = value;
      return newList;
    });
  };
  // 添加API
  const addApi = () => {
    // 判断如果有在editing的，则提示先保存
    const editingApi = apiList.find(item => item.isEditing);
    if (editingApi) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'ai.agent.script.settings.not.empty.tips',
          defaultValue: '请先保存当前编辑的API',
        }),
      });
      return;
    }
    const newApi = {
      id: `${apiList.length + 1}`,
      name: `API ${apiList.length + 1}`,
      editApiText: `API ${apiList.length + 1}`,
      isEditing: false,
      method: 'GET',
      url: '',
      activeBodyType: 'form-data',
      activeTab: 'params',
      params: [
        {
          id: 'p1',
          key: '',
          value: '',
          description: '',
        },
      ],
      headers: [
        {
          id: 'h1',
          key: 'Accept',
          value: '*/*',
          description: '',
        },
        {
          id: 'h2',
          key: 'Accept-Encoding',
          value: 'gzip, deflate, br',
          description: '',
        },
        {
          id: 'h3',
          key: 'Content-Type',
          value: 'application/json',
          description: '',
        },
        {
          id: 'h5',
          key: 'Connection',
          value: 'keep-alive',
          description: '',
        },
      ],
      authentication: {
        isAuthenticated: false,
        authType: 1,
        apiKey: '',
        header: '',
      },
      body: {
        formData: [],
        raw: {
          content: '{\n  \n}',
        },
      },
      response: {
        body: '',
      },
    };
    setText('');
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      newList.unshift(newApi);
      return newList;
    });
    setActiveApiTabIndex(0);
    // 直接保存
    saveApiList(newApi, 0);
  };
  // 编辑API
  const editApi = (item, index) => {
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      newList[index].isEditing = true;
      newList[index].editApiText = item.name;
      return newList;
    });
  };
  // 删除API
  const deleteApi = (item, index) => {
    setLoading(true);
    dispatch({
      type: 'apiManage/deleteApi',
      payload: item.apiId,
      callback: response => {
        console.log(response);
        // 在删除后重置activeApiTabIndex
        if (apiList.length > 1) {
          // 如果删除的不是最后一个，则选择前一个
          setActiveApiTabIndex(Math.max(0, index - 1));
        } else {
          // 如果删除的是最后一个，则重置为0
          setActiveApiTabIndex(0);
        }
        getApiList();
      },
    });
  };
  // 取消编辑API
  const cancelApi = (item, index) => {
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      newList[index].isEditing = false;
      newList[index].editApiText = '';
      return newList;
    });
  };
  // 保存API列表
  const saveApiList = (item, index) => {
    setLoading(true);
    const data = {
      apiName: item.name,
      apiMethod: item.method,
      apiPath: item.url,
      bodyType: item.activeBodyType === 'form-data' ? 2 : 1,
      params: JSON.stringify(item.params),
      header: JSON.stringify(item.headers),
      body: JSON.stringify(item.body),
      authentication: item.authentication,
    };
    if (item.apiId) {
      data.apiId = item.apiId;
      dispatch({
        type: 'apiManage/updateApi',
        payload: data,
        callback: response => {
          getApiList();
          console.log(response);
        },
      });
    } else {
      dispatch({
        type: 'apiManage/saveApi',
        payload: data,
        callback: response => {
          getApiList();
          console.log(response);
        },
      });
    }
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      newList[index].isEditing = false;
      newList[index].name = newList[index].editApiText;
      newList[index].editApiText = '';
      return newList;
    });
  };
  // 切换请求体tab (params, headers, body)
  const handleBodyTabChange = tabIndex => {
    const tabMapping = {
      0: 'params',
      1: 'headers',
      2: 'body',
      3: 'authentication',
    };

    const activeTab = tabMapping[tabIndex];
    setActiveApiTestBodyTab(tabIndex);
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      // 更新activeTab,保持其他数据不变
      if (newList[activeApiTabIndex]) {
        newList[activeApiTabIndex] = {
          ...newList[activeApiTabIndex],
          activeTab,
          body: {
            formData: newList[activeApiTabIndex].body.formData,
            raw: {
              content: newList[activeApiTabIndex].body.raw.content,
            },
          },
        };
      }
      return newList;
    });
  };

  // 切换body类型 (form-data, raw)
  const handleBodyTypeChange = type => {
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      // 更新activeBodyType
      if (newList[activeApiTabIndex]) {
        newList[activeApiTabIndex] = {
          ...newList[activeApiTabIndex],
          activeBodyType: type,
          body: {
            formData: newList[activeApiTabIndex].body.formData,
            raw: {
              content: newList[activeApiTabIndex].body.raw.content,
            },
          },
        };
      }
      return newList;
    });
  };

  // 添加参数行
  const addParamRow = type => {
    const newList = [...apiList];
    const currentApi = newList[activeApiTabIndex];
    const newRow = {
      id: `${type[0]}${Date.now()}`,
      key: '',
      value: '',
      description: '',
    };
    if (type === 'params') {
      currentApi.params.push(newRow);
    } else if (type === 'headers') {
      currentApi.headers.push(newRow);
    } else if (type === 'formData') {
      currentApi.body.formData.push({ ...newRow, type: 'text' });
    }
    setApiList(newList);
  };
  // 删除参数行
  const deleteRow = (id, type) => {
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      const currentApi = newList[activeApiTabIndex];
      if (type === 'params') {
        // 删除所选中的行
        currentApi.params = currentApi.params.filter(item => item.id !== id);
      } else if (type === 'headers') {
        currentApi.headers = currentApi.headers.filter(item => item.id !== id);
      } else if (type === 'formData') {
        currentApi.body.formData = currentApi.body.formData.filter(
          item => item.id !== id,
        );
      }
      return newList;
    });
  };
  // 更新参数行
  const updateRow = (type, id, field, value) => {
    setApiList(prevList => {
      // 创建深拷贝
      const newList = prevList.map(item => ({ ...item }));
      const currentApi = newList[activeApiTabIndex];
      if (currentApi) {
        let targetArray;
        if (type === 'params') {
          // 创建params数组的深拷贝
          currentApi.params = currentApi.params.map(param => ({ ...param }));
          targetArray = currentApi.params;
        } else if (type === 'headers') {
          // 创建headers数组的深拷贝
          currentApi.headers = currentApi.headers.map(header => ({
            ...header,
          }));
          targetArray = currentApi.headers;
        } else if (type === 'formData') {
          // 创建formData数组的深拷贝
          currentApi.body = {
            ...currentApi.body,
            formData: currentApi.body.formData.map(form => ({ ...form })),
          };
          targetArray = currentApi.body.formData;
        }
        const rowIndex = targetArray.findIndex(row => row.id === id);
        if (rowIndex !== -1) {
          // 创建新的对象来更新字段
          targetArray[rowIndex] = {
            ...targetArray[rowIndex],
            [field]: value,
          };
        }
      }
      return newList;
    });
  };

  // 更新请求方法
  const apiMethodChange = value => {
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      newList[activeApiTabIndex].method = value;
      return newList;
    });
  };

  // 更新URL
  const handleUrlChange = val => {
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      newList[activeApiTabIndex].url = val;
      return newList;
    });
  };
  // 测试API
  const testApi = async () => {
    // 如果url为空，则提示请先输入URL
    if (!apiList[activeApiTabIndex].url) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'ai.agent.api.url.placeholder.tips',
          defaultValue: '请先输入API接口地址',
        }),
      });
      return;
    }

    setLoading(true);
    const currentApi = apiList[activeApiTabIndex];
    const data = {
      apiPath: currentApi.url,
      bodyType: currentApi.activeBodyType === 'form-data' ? 2 : 1,
      params: JSON.stringify(currentApi.params),
      header: JSON.stringify(currentApi.headers),
      body: JSON.stringify(currentApi.body),
      authorization: JSON.stringify(currentApi.authentication),
      apiName: currentApi.name,
      apiMethod: currentApi.method,
    };
    // 将对象转为缩进文本（key: value）
    const formatObject = (obj, indentLevel = 0) => {
      const indent = '  '.repeat(indentLevel);
      let result = '';
      for (const key in obj) {
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          result += `${indent}${key}: {\n${formatObject(
            obj[key],
            indentLevel + 1,
          )}${indent}}\n`;
        } else {
          result += `${indent}${key}: ${obj[key]}\n`;
        }
      }
      return result;
    };

    dispatch({
      type: 'apiManage/testApi',
      payload: data,
      callback: response => {
        setLoading(false);
        if (response.code === 200) {
          // const parsed = JSON.parse(response.data.body);
          setApiList(prevList => {
            const newList = prevList.map(item => ({ ...item }));
            newList[activeApiTabIndex].response.body = response.data.body;
            return newList;
          });
        } else {
          notification.error({
            message: response.msg || '测试请求失败',
          });
        }
      },
      error: error => {
        setLoading(false);
        notification.error({
          message: '测试请求失败',
        });
      },
    });
  };

  // 保存API
  const saveApi = async () => {
    // apiList[activeApiTabIndex].body.raw.content = text;
    setLoading(true);
    const data = {
      apiName: apiList[activeApiTabIndex].name,
      apiMethod: apiList[activeApiTabIndex].method,
      apiPath: apiList[activeApiTabIndex].url,
      bodyType:
        apiList[activeApiTabIndex].activeBodyType === 'form-data' ? 2 : 1,
      params: JSON.stringify(apiList[activeApiTabIndex].params),
      header: JSON.stringify(apiList[activeApiTabIndex].headers),
      body: JSON.stringify(apiList[activeApiTabIndex].body),
      authorization: JSON.stringify(apiList[activeApiTabIndex].authentication),
    };
    if (apiList[activeApiTabIndex].apiId) {
      data.apiId = apiList[activeApiTabIndex].apiId;
      dispatch({
        type: 'apiManage/updateApi',
        payload: data,
        callback: response => {
          setLoading(false);
          console.log(response);
        },
      });
    } else {
      dispatch({
        type: 'apiManage/saveApi',
        payload: data,
        callback: response => {
          setLoading(false);
          console.log(response);
        },
      });
    }
  };

  // 通用表格组件
  const CommonTable = ({ data, type, onAdd, onUpdate, onDelete }) => {
    const [localValues, setLocalValues] = useState({}); // 解决输入列表更新重新渲染 input丢失光标
    // 处理输入变化
    const handleInputChange = (id, field, value) => {
      // 更新本地状态
      setLocalValues(prev => ({
        ...prev,
        [`${id}-${field}`]: value,
      }));
    };

    // 获取输入值（优先使用本地状态）
    const getInputValue = (record, field) => {
      const localKey = `${record.id}-${field}`;
      return localValues[localKey] !== undefined
        ? localValues[localKey]
        : record[field];
    };
    const onTableDelete = id => {
      onDelete(id);
    };
    const tableVarClick = (e, record) => {
      sessionStorage.setItem('headerItem', JSON.stringify(record));
    };
    const columns = [
      {
        title: 'Key',
        dataIndex: 'key',
        width: 200,
        render: (text, record) => (
          <Input
            value={getInputValue(record, 'key')}
            onChange={e => handleInputChange(record.id, 'key', e.target.value)}
            onBlur={() =>
              onUpdate(type, record.id, 'key', getInputValue(record, 'key'))
            }
            placeholder={getIntl().formatMessage({
              id: 'ai.agent.api.table.key.placeholder',
              defaultValue: '请输入Key',
            })}
          />
        ),
      },
      {
        title: 'Value',
        dataIndex: 'value',
        width: 200,
        render: (text, record) =>
          type === 'headers' ? (
            <Input
              placeholder={getIntl().formatMessage({
                id: 'ai.agent.api.table.value.placeholder',
                defaultValue: '请输入对应的值',
              })}
              value={getInputValue(record, 'value')}
              onChange={e =>
                handleInputChange(record.id, 'value', e.target.value)
              }
              onBlur={() =>
                onUpdate(
                  type,
                  record.id,
                  'value',
                  getInputValue(record, 'value'),
                )
              }
              addonAfter={
                <Dropdown
                  menu={{
                    items: tableItems,
                  }}
                  trigger={['click']}
                  onClick={e => tableVarClick(e, record)}
                >
                  <span>{ConstIcon()}</span>
                </Dropdown>
              }
            />
          ) : (
            <Input
              value={getInputValue(record, 'value')}
              onChange={e =>
                handleInputChange(record.id, 'value', e.target.value)
              }
              onBlur={() =>
                onUpdate(
                  type,
                  record.id,
                  'value',
                  getInputValue(record, 'value'),
                )
              }
              placeholder={getIntl().formatMessage({
                id: 'ai.agent.api.table.value.placeholder',
                defaultValue: '请输入对应的值',
              })}
            />
          ),
      },
      {
        title: getIntl().formatMessage({
          id: 'ai.agent.api.table.description.title',
          defaultValue: '描述',
        }),
        dataIndex: 'description',
        render: (text, record) => (
          <Input
            value={getInputValue(record, 'description')}
            onChange={e =>
              handleInputChange(record.id, 'description', e.target.value)
            }
            onBlur={() =>
              onUpdate(
                type,
                record.id,
                'description',
                getInputValue(record, 'description'),
              )
            }
            placeholder={getIntl().formatMessage({
              id: 'ai.agent.api.table.description.placeholder',
              defaultValue: '请输入描述',
            })}
          />
        ),
      },
      {
        title: getIntl().formatMessage({
          id: 'ai.agent.api.table.operation.title',
          defaultValue: '操作',
        }),
        dataIndex: 'operation',
        width: 100,
        render: (text, record) =>
          // 默认只有一个不显示删除按钮
          data.length > 1 ? (
            <DeleteHome onClick={() => onTableDelete(record.id)} />
          ) : (
            <></>
          ),
      },
    ];

    return (
      <div className={styles.api_manage_right_table}>
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          size="small"
          rowKey="id"
          bordered={true}
        />
        <div className={styles.api_manage_right_table_header}>
          <Button icon={<PlusOutlined />} onClick={() => onAdd(type)}>
            {getIntl().formatMessage({
              id: 'ai.agent.api.table.operation.add',
              defaultValue: '添加',
            })}
          </Button>
        </div>
      </div>
    );
  };

  // 参数表格组件
  const ParamsTable = ({ data, onAdd, onUpdate, onDelete }) => (
    <CommonTable
      data={data}
      type="params"
      onAdd={onAdd}
      onUpdate={onUpdate}
      onDelete={onDelete}
    />
  );

  // 请求头表格组件
  const HeadersTable = ({ data, onAdd, onUpdate, onDelete }) => (
    <CommonTable
      data={data}
      type="headers"
      onAdd={onAdd}
      onUpdate={onUpdate}
      onDelete={onDelete}
    />
  );

  // 表单数据表格组件
  const FormDataTable = ({ data, onAdd, onUpdate, onDelete }) => (
    <CommonTable
      data={data}
      type="formData"
      onAdd={onAdd}
      onUpdate={onUpdate}
      onDelete={onDelete}
    />
  );
  useEffect(() => {
    queryCurrentVar();
  }, []);
  const [items, setItems] = useState([]); // 请求地址变量
  const [tableItems, setTableItems] = useState([]); // 请求头变量
  const [authHeaderItems, setAuthHeaderItems] = useState([]); // 身份验证header变量
  const [authApiKeyItems, setAuthApiKeyItems] = useState([]); // 身份验证api Key变量
  const [text, setText] = useState('');
  const handleVarClickValue = async value => {
    console.log(value);
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      newList[activeApiTabIndex].url = newList[activeApiTabIndex].url + value;
      return newList;
    });
  };
  // Headers
  const handleVarTableClickValue = async value => {
    let headerItem = JSON.parse(sessionStorage.getItem('headerItem'));
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      let index = newList[activeApiTabIndex].headers.findIndex(
        item => item.id === headerItem.id,
      );
      newList[activeApiTabIndex].headers[index].value =
        newList[activeApiTabIndex].headers[index].value + value;
      return newList;
    });
  };
  // Authentication Header 变量
  const handleVarAuthHeader = async value => {
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      newList[activeApiTabIndex].authentication.header =
        newList[activeApiTabIndex].authentication.header + value;
      return newList;
    });
  };
  // Authentication API Key
  const handleVarAuthApiKey = async value => {
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      newList[activeApiTabIndex].authentication.apiKey =
        newList[activeApiTabIndex].authentication.apiKey + value;
      return newList;
    });
  };
  // 身份验证header输入
  const handleAuthHeaderInput = async value => {
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      newList[activeApiTabIndex].authentication.header = value;
      return newList;
    });
  };
  // 身份验证api key输入
  const handleVarAuthApiKeyInput = async value => {
    setApiList(prevList => {
      const newList = prevList.map(item => ({ ...item }));
      newList[activeApiTabIndex].authentication.apiKey = value;
      return newList;
    });
  };
  // 查询变量
  const queryCurrentVar = () => {
    try {
      let aiAgentId = localStorage.getItem('aiAgentId');
      dispatch({
        type: 'aiagent/queryCurrentVar',
        payload: {
          id: aiAgentId, // 智能体 ID
        },
        callback: response => {
          let { code, data, msg } = response;
          if (200 === code) {
            let arr3 = [];
            let arr2 = [];
            let arr4 = [];
            let arr1 = [];
            // 变量类型（1.系统内置变量，2当前会话变量，3当前智能体变量，4全局变量）
            data?.forEach(item => {
              if (item.variableType === 3) {
                arr3.push(item);
              } else if (item.variableType === 2) {
                arr2.push(item);
              } else if (item.variableType === 4) {
                arr4.push(item);
              } else if (item.variableType === 1) {
                arr1.push(item);
              }
            });
            // items:测试url变量，TableItems:请求头变量,
            setItems([
              {
                key: '3',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.1',
                  defaultValue: '当前智能体变量',
                }),
                children: arr3.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarClickValue(
                        '{var_agent.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '2',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.2',
                  defaultValue: '当前会话变量',
                }),
                children: arr2.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarClickValue(
                        '{var_session.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '4',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.3',
                  defaultValue: '全局变量',
                }),
                children: arr4.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarClickValue(
                        '{var_global.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '1',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.4',
                  defaultValue: '系统内置变量',
                }),
                children: arr1.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarClickValue(
                        '{var_system.' + item.variableName + '}',
                      ),
                  };
                }),
              },
            ]);
            setTableItems([
              {
                key: '3',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.1',
                  defaultValue: '当前智能体变量',
                }),
                children: arr3.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarTableClickValue(
                        '{var_agent.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '2',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.2',
                  defaultValue: '当前会话变量',
                }),
                children: arr2.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarTableClickValue(
                        '{var_session.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '4',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.3',
                  defaultValue: '全局变量',
                }),
                children: arr4.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarTableClickValue(
                        '{var_global.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '1',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.4',
                  defaultValue: '系统内置变量',
                }),
                children: arr1.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarTableClickValue(
                        '{var_system.' + item.variableName + '}',
                      ),
                  };
                }),
              },
            ]);
            setAuthHeaderItems([
              {
                key: '3',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.1',
                  defaultValue: '当前智能体变量',
                }),
                children: arr3.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarAuthHeader(
                        '{var_agent.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '2',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.2',
                  defaultValue: '当前会话变量',
                }),
                children: arr2.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarAuthHeader(
                        '{var_session.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '4',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.3',
                  defaultValue: '全局变量',
                }),
                children: arr4.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarAuthHeader(
                        '{var_global.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '1',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.4',
                  defaultValue: '系统内置变量',
                }),
                children: arr1.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarAuthHeader(
                        '{var_system.' + item.variableName + '}',
                      ),
                  };
                }),
              },
            ]);
            setAuthApiKeyItems([
              {
                key: '3',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.1',
                  defaultValue: '当前智能体变量',
                }),
                children: arr3.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarAuthApiKey(
                        '{var_agent.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '2',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.2',
                  defaultValue: '当前会话变量',
                }),
                children: arr2.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarAuthApiKey(
                        '{var_session.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '4',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.3',
                  defaultValue: '全局变量',
                }),
                children: arr4.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarAuthApiKey(
                        '{var_global.' + item.variableName + '}',
                      ),
                  };
                }),
              },
              {
                key: '1',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.4',
                  defaultValue: '系统内置变量',
                }),
                children: arr1.map(item => {
                  return {
                    key: item.variableId,
                    label: item.variableName,
                    onClick: () =>
                      handleVarAuthApiKey(
                        '{var_system.' + item.variableName + '}',
                      ),
                  };
                }),
              },
            ]);
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    } catch (e) {
      console.log(e);
    }
  };
  // 更新raw内容
  const updateRawContent = content => {
    setApiList(prevList => {
      console.log(prevList, 'prevListprevList');
      const newList = prevList.map(item => ({ ...item }));
      newList[activeApiTabIndex].body.raw.content = content;
      return newList;
    });
  };
  return (
    <Spin spinning={loading}>
      <div
        className={styles.canvasFlow_apiManageModal}
        // onKeyDown={e => {
        //   // 阻止回车键的默认行为
        //   if (e.key === 'Enter') {
        //     e.preventDefault();
        //   }
        // }}
      >
        <div className={styles.api_manage_left}>
          <div className={styles.search_box}>
            <Input
              placeholder={getIntl().formatMessage({
                id: 'ai.agent.api.search.placeholder',
                defaultValue: '搜索',
              })}
              onPressEnter={value => searchApi(value)}
              onChange={e => setSearchApiText(e.target.value)}
              style={{ width: '100%' }}
              prefix={<Search />}
            />
          </div>
          <div className={styles.api_list}>
            {apiList.length > 0 ? (
              apiList.map((item, index) => (
                <div
                  key={index}
                  className={styles.list_item}
                  onClick={() => handleTabChange(index)}
                >
                  {item.isEditing ? (
                    <div className={styles.item_content_input}>
                      <Input
                        value={item.editApiText}
                        onChange={e => editApiTextChange(e.target.value, index)}
                      />
                    </div>
                  ) : (
                    <div className={styles.item_content}>{item.name}</div>
                  )}
                  <div className={styles.item_operation}>
                    {item.isEditing ? (
                      <>
                        <img
                          onClick={() => saveApiList(item, index)}
                          src={SaveReplyIcon}
                        />
                        <Popconfirm
                          title={getIntl().formatMessage({
                            id: 'new.intent.add.cancel.confirm',
                            defaultValue: '取消将清空表单，确定取消么？',
                          })}
                          onConfirm={() => cancelApi(item, index)}
                        >
                          <img src={CancelSaveReplyIcon} />
                        </Popconfirm>
                      </>
                    ) : (
                      <>
                        <img
                          onClick={() => editApi(item, index)}
                          src={EditorReplyIcon}
                        />
                        <Popconfirm
                          disabled={item.editApiText !== ''}
                          title={getIntl().formatMessage({
                            id:
                              'document.knowledge.base.table.operation.delete.Popconfirm',
                            defaultValue: '确定删除该条数据吗？',
                          })}
                          onConfirm={() => deleteApi(item, index)}
                        >
                          <img src={DeleteReplyIcon} />
                        </Popconfirm>
                      </>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <></>
            )}
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            className={styles.add_api_button}
            onClick={addApi}
          >
            {getIntl().formatMessage({
              id: 'ai.agent.api.operation.add',
              defaultValue: '添加API接口',
            })}
          </Button>
        </div>
        {/* 右侧API详情 */}
        <div className={styles.api_manage_right}>
          {apiList.length > 0 ? (
            <>
              {/* API选项卡 */}
              <div className={styles.api_manage_right_tabs}>
                {apiList.map((item, index) => (
                  <div
                    key={index}
                    className={`${styles.api_manage_right_tab} ${
                      index === activeApiTabIndex
                        ? styles.api_manage_right_tab_active
                        : ''
                    }`}
                    onClick={() => handleTabChange(index)}
                  >
                    {item.name}
                  </div>
                ))}
              </div>
              {/* 请求方法和URL */}
              <div className={styles.api_manage_right_api_test}>
                <div className={styles.api_manage_right_api_test_method}>
                  <Select
                    value={apiList[activeApiTabIndex].method}
                    style={{ width: 78 }}
                    onChange={apiMethodChange}
                    options={apiMethodOptions}
                  />
                </div>
                <div className={styles.api_manage_right_api_test_url}>
                  <Input
                    style={{ height: '32px' }}
                    placeholder={getIntl().formatMessage({
                      id: 'ai.agent.api.url.placeholder',
                      defaultValue: '输入API接口地址',
                    })}
                    value={apiList[activeApiTabIndex].url}
                    onInput={e => handleUrlChange(e.target.value)}
                    addonAfter={
                      <Dropdown
                        menu={{
                          items,
                        }}
                        trigger={['click']}
                      >
                        <span>{ConstIcon()}</span>
                      </Dropdown>
                    }
                  />
                </div>
                <div className={styles.api_manage_right_api_test_button}>
                  <Button onClick={testApi}>
                    {getIntl().formatMessage({
                      id: 'ai.agent.api.operation.test.btn',
                      defaultValue: '测试',
                    })}
                  </Button>
                </div>
                <div className={styles.api_manage_right_api_test_save}>
                  <Button type="primary" onClick={saveApi}>
                    {getIntl().formatMessage({
                      id: 'ai.agent.api.operation.save.btn',
                      defaultValue: '保存',
                    })}
                  </Button>
                </div>
              </div>
              {/* 请求体选项卡 */}
              <div className={styles.api_manage_right_api_test_body}>
                <div className={styles.api_manage_right_api_test_body_tab}>
                  <div
                    className={`${styles.api_test_body_tab_item} ${
                      activeApiTestBodyTab === 0
                        ? styles.api_test_body_tab_item_active
                        : ''
                    }`}
                    onClick={() => handleBodyTabChange(0)}
                  >
                    Params
                  </div>
                  <div
                    className={`${styles.api_test_body_tab_item} ${
                      activeApiTestBodyTab === 1
                        ? styles.api_test_body_tab_item_active
                        : ''
                    }`}
                    onClick={() => handleBodyTabChange(1)}
                  >
                    Headers
                  </div>
                  <div
                    className={`${styles.api_test_body_tab_item} ${
                      activeApiTestBodyTab === 3
                        ? styles.api_test_body_tab_item_active
                        : ''
                    }`}
                    onClick={() => handleBodyTabChange(3)}
                  >
                    Authentication
                  </div>
                  <div
                    className={`${styles.api_test_body_tab_item} ${
                      activeApiTestBodyTab === 2
                        ? styles.api_test_body_tab_item_active
                        : ''
                    }`}
                    onClick={() => handleBodyTabChange(2)}
                  >
                    Body
                  </div>
                </div>
                {/* 参数内容区域 */}
                <div className={styles.api_test_body_content}>
                  {activeApiTestBodyTab === 0 && (
                    <>
                      <div className={styles.api_test_body_content_title}>
                        Query Params
                      </div>
                      <ParamsTable
                        data={apiList[activeApiTabIndex].params}
                        onAdd={() => addParamRow('params')}
                        onUpdate={(type, id, field, value) =>
                          updateRow(type, id, field, value)
                        }
                        onDelete={id => deleteRow(id, 'params')}
                      />
                    </>
                  )}
                  {activeApiTestBodyTab === 1 && (
                    <>
                      <div className={styles.api_test_body_content_title}>
                        Headers
                      </div>
                      <HeadersTable
                        data={apiList[activeApiTabIndex].headers}
                        onAdd={() => addParamRow('headers')}
                        onUpdate={(type, id, field, value) =>
                          updateRow(type, id, field, value)
                        }
                        onDelete={id => deleteRow(id, 'headers')}
                      />
                    </>
                  )}
                  {activeApiTestBodyTab === 2 && (
                    <div className={styles.body_content}>
                      <div className={styles.body_type_selector}>
                        <Radio.Group
                          value={apiList[activeApiTabIndex].activeBodyType}
                          onChange={e => handleBodyTypeChange(e.target.value)}
                        >
                          <Radio value="form-data">form-data</Radio>
                          <Radio value="raw">raw</Radio>
                        </Radio.Group>
                      </div>
                      {apiList[activeApiTabIndex].activeBodyType ===
                      'form-data' ? (
                        <FormDataTable
                          data={apiList[activeApiTabIndex].body.formData}
                          onAdd={() => addParamRow('formData')}
                          onUpdate={(type, id, field, value) =>
                            updateRow(type, id, field, value)
                          }
                          onDelete={id => deleteRow(id, 'formData')}
                        />
                      ) : (
                        <div className={styles.raw_editor}>
                          <HighLightTextarea
                            setText={setText}
                            text={apiList[activeApiTabIndex].body.raw.content}
                            onChange={e => updateRawContent(e)}
                            noEmoji={true}
                          ></HighLightTextarea>
                        </div>
                      )}
                    </div>
                  )}
                  {activeApiTestBodyTab === 3 && (
                    <div className={styles.api_auth}>
                      <Checkbox
                        checked={
                          apiList[activeApiTabIndex].authentication
                            .isAuthenticated
                        }
                        onChange={e => {
                          setApiList(prevList => {
                            const newList = prevList.map(item => ({
                              ...item,
                            }));
                            newList[
                              activeApiTabIndex
                            ].authentication.isAuthenticated = e.target.checked;
                            return newList;
                          });
                        }}
                      >
                        {getIntl().formatMessage({
                          id: 'ai.agent.api.authentication.open',
                        })}
                      </Checkbox>
                      {apiList[activeApiTabIndex].authentication
                        .isAuthenticated && (
                        <>
                          <div className={styles.api_auth_type}>
                            {getIntl().formatMessage({
                              id: 'ai.agent.api.authentication.type',
                            })}
                          </div>
                          <Radio.Group
                            value={
                              apiList[activeApiTabIndex].authentication.authType
                            }
                            onChange={e => {
                              setApiList(prevList => {
                                const newList = prevList.map(item => ({
                                  ...item,
                                }));
                                newList[
                                  activeApiTabIndex
                                ].authentication.authType = e.target.value;
                                return newList;
                              });
                            }}
                          >
                            <Radio value="1">
                              {getIntl().formatMessage({
                                id: 'ai.agent.api.authentication.basic',
                              })}
                            </Radio>
                            <Radio value="2">
                              {getIntl().formatMessage({
                                id: 'ai.agent.api.authentication.bearer',
                              })}
                            </Radio>
                            <Radio value="3">
                              {getIntl().formatMessage({
                                id: 'ai.agent.api.authentication.custom',
                              })}
                            </Radio>
                          </Radio.Group>
                          {apiList[activeApiTabIndex].authentication
                            .authType === '3' && (
                            <>
                              <div className={styles.api_auth_label}>
                                Header
                              </div>
                              <div className={styles.api_auth_input}>
                                <Input
                                  value={
                                    apiList[activeApiTabIndex].authentication
                                      .header
                                  }
                                  placeholder={getIntl().formatMessage({
                                    id:
                                      'ai.agent.api.authentication.header.placeholder',
                                  })}
                                  onInput={e =>
                                    handleAuthHeaderInput(e.target.value)
                                  }
                                  addonAfter={
                                    <Dropdown
                                      menu={{
                                        items: authHeaderItems,
                                      }}
                                      trigger={['click']}
                                    >
                                      <span>{ConstIcon()}</span>
                                    </Dropdown>
                                  }
                                />
                              </div>
                            </>
                          )}
                          <div className={styles.api_auth_label}>API Key</div>
                          <div className={styles.api_auth_input}>
                            <Input
                              value={
                                apiList[activeApiTabIndex].authentication.apiKey
                              }
                              placeholder={getIntl().formatMessage({
                                id:
                                  'ai.agent.api.authentication.apiKey.placeholder',
                              })}
                              onInput={e =>
                                handleVarAuthApiKeyInput(e.target.value)
                              }
                              addonAfter={
                                <Dropdown
                                  menu={{
                                    items: authApiKeyItems,
                                  }}
                                  trigger={['click']}
                                >
                                  <span>{ConstIcon()}</span>
                                </Dropdown>
                              }
                            />
                          </div>
                        </>
                      )}
                    </div>
                  )}
                </div>
              </div>
              {/* 测试结果 */}
              {activeApiTestBodyTab === 3 ? (
                apiList[activeApiTabIndex].authentication.isAuthenticated && (
                  <div className={styles.api_manage_right_table_response}>
                    <div
                      className={styles.api_manage_right_table_response_title}
                    >
                      {getIntl().formatMessage({
                        id: 'ai.agent.api.response.title',
                        defaultValue: '测试结果',
                      })}
                    </div>
                    {/* textarea */}
                    <div
                      className={
                        styles.api_manage_right_table_response_textarea
                      }
                    >
                      <TextArea
                        value={apiList[activeApiTabIndex].response.body}
                        style={{ height: 200, whiteSpace: 'pre-wrap' }}
                        readOnly
                      />
                    </div>
                  </div>
                )
              ) : (
                <div className={styles.api_manage_right_table_response}>
                  <div className={styles.api_manage_right_table_response_title}>
                    {getIntl().formatMessage({
                      id: 'ai.agent.api.response.title',
                      defaultValue: '测试结果',
                    })}
                  </div>
                  {/* textarea */}
                  <div
                    className={styles.api_manage_right_table_response_textarea}
                  >
                    <TextArea
                      value={apiList[activeApiTabIndex].response.body}
                      style={{ height: 200, whiteSpace: 'pre-wrap' }}
                      readOnly
                    />
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className={styles.api_manage_right_empty}>
              <img src={ApiEmptyIcon} />
              <span>
                {getIntl().formatMessage({
                  id: 'ai.agent.api.table.empty.text',
                  defaultValue: '您目前还没有添加 API 接口',
                })}
              </span>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                style={{ width: '175px' }}
                onClick={() => addApi()}
              >
                {getIntl().formatMessage({
                  id: 'ai.agent.api.operation.add',
                  defaultValue: '添加API接口',
                })}
              </Button>
            </div>
          )}
        </div>
      </div>
    </Spin>
  );
});

export default CanvasFlow;
